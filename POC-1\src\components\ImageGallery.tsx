import React, { useState, useEffect } from 'react';
import { Search, Filter, Eye, Download, Calendar, MapPin, X, ChevronLeft, ChevronRight, Camera } from 'lucide-react';

interface GalleryImage {
  name: string;
  folder: string;
  brand: string;
  base64: string;
  path: string;
  state: string;
  timestamp: string;
  coordinates?: string;
  location?: string;
}

interface ImageGalleryProps {
  state?: string;
  onStateChange?: (state: string) => void;
  searchQuery?: string;
  showCompetitors?: boolean;
}

export const ImageGallery: React.FC<ImageGalleryProps> = ({
  state = 'Delhi',
  onStateChange,
  searchQuery: propSearchQuery = '',
  showCompetitors: propShowCompetitors = false
}) => {
  const [images, setImages] = useState<GalleryImage[]>([]);
  const [filteredImages, setFilteredImages] = useState<GalleryImage[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState(propSearchQuery);
  const [selectedBrand, setSelectedBrand] = useState('');
  const [showCompetitors, setShowCompetitors] = useState(propShowCompetitors);
  const [selectedImage, setSelectedImage] = useState<GalleryImage | null>(null);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  // Exact states from Streamlit app
  const states = ['Delhi(NCT)', 'West Bengal', 'Maharashtra', 'Karnataka', 'Tamil Nadu'];

  useEffect(() => {
    loadImages();
  }, [state, showCompetitors, searchQuery]);

  useEffect(() => {
    filterImages();
  }, [images, searchQuery, selectedBrand]);

  const loadImages = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const params = new URLSearchParams({
        state,
        show_competitors: showCompetitors.toString(),
        search: searchQuery
      });
      
      const response = await fetch(`/api/interactive/images?${params}`);
      if (!response.ok) {
        throw new Error('Failed to load images');
      }
      
      const data = await response.json();
      setImages(data.images || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load images');
    } finally {
      setLoading(false);
    }
  };

  const filterImages = () => {
    let filtered = images;

    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(img =>
        img.name.toLowerCase().includes(query) ||
        img.brand.toLowerCase().includes(query) ||
        img.folder.toLowerCase().includes(query)
      );
    }

    if (selectedBrand) {
      filtered = filtered.filter(img =>
        img.brand.toLowerCase().includes(selectedBrand.toLowerCase())
      );
    }

    setFilteredImages(filtered);
  };

  const openModal = (image: GalleryImage, index: number) => {
    setSelectedImage(image);
    setCurrentImageIndex(index);
  };

  const closeModal = () => {
    setSelectedImage(null);
  };

  const navigateImage = (direction: 'prev' | 'next') => {
    const newIndex = direction === 'prev' 
      ? (currentImageIndex - 1 + filteredImages.length) % filteredImages.length
      : (currentImageIndex + 1) % filteredImages.length;
    
    setCurrentImageIndex(newIndex);
    setSelectedImage(filteredImages[newIndex]);
  };

  const downloadImage = (image: GalleryImage) => {
    const link = document.createElement('a');
    link.href = `data:image/jpeg;base64,${image.base64}`;
    link.download = image.name;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const uniqueBrands = Array.from(new Set(images.map(img => img.brand)));

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96 bg-gray-50 rounded-lg">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading images...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
        <p className="text-red-600 font-medium">Error loading images</p>
        <p className="text-red-500 text-sm mt-1">{error}</p>
        <button
          onClick={loadImages}
          className="mt-3 px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header - Exact match with Streamlit */}
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold text-gray-800 mb-2 flex items-center justify-center">
          <Camera className="w-6 h-6 mr-2" />
          📸 Interactive Image Gallery
        </h2>
        {searchQuery && !showCompetitors && (
          <p className="text-gray-600 italic">Showing images for brand: <strong>{searchQuery}</strong></p>
        )}
        {searchQuery && showCompetitors && (
          <p className="text-gray-600 italic">Showing all competitor images (including <strong>{searchQuery}</strong>)</p>
        )}
        {showCompetitors && !searchQuery && (
          <p className="text-gray-600 italic">Showing all competitor images</p>
        )}
      </div>

      {/* Controls - Streamlit style */}
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {/* State Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">State</label>
            <select
              value={state}
              onChange={(e) => onStateChange?.(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 outline-none"
              style={{ fontSize: '14px' }}
            >
              {states.map(s => (
                <option key={s} value={s}>{s}</option>
              ))}
            </select>
          </div>

          {/* Brand Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Brand Search</label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search brand (Amaron, Exide, Tata)..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 outline-none"
                style={{ fontSize: '14px' }}
              />
            </div>
          </div>

          {/* Show Competitors Toggle */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Display Options</label>
            <div className="flex items-center">
              <input
                type="checkbox"
                id="showCompetitors"
                checked={showCompetitors}
                onChange={(e) => setShowCompetitors(e.target.checked)}
                className="mr-2 w-4 h-4 text-orange-600 bg-gray-100 border-gray-300 rounded focus:ring-orange-500"
              />
              <label htmlFor="showCompetitors" className="text-sm text-gray-700">
                Show All Competitors
              </label>
            </div>
          </div>

          {/* Stats Display */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Gallery Stats</label>
            <div className="text-sm text-gray-600 space-y-1">
              <div>{filteredImages.length} images found</div>
              <div>State: {state}</div>
            </div>
          </div>
        </div>
      </div>

      {/* Gallery Grid - Exact Streamlit styling */}
      {filteredImages.length > 0 ? (
        <div
          className="bg-transparent rounded-lg p-0 m-5 shadow-none"
          style={{
            background: 'transparent',
            borderRadius: '15px',
            padding: '0',
            margin: '20px 0',
            boxShadow: 'none'
          }}
        >
          {/* Gallery Stats Header */}
          <div
            className="text-center mb-6 p-4"
            style={{
              textAlign: 'center',
              marginBottom: '25px',
              padding: '15px 25px',
              background: 'linear-gradient(135deg, #ff6600 0%, #ff8533 100%)',
              color: 'white',
              borderRadius: '12px 12px 0 0',
              fontWeight: 'bold',
              fontSize: '16px'
            }}
          >
            <h3 style={{ margin: '0 0 8px 0', fontSize: '18px' }}>📸 Interactive Image Gallery</h3>
            <div style={{ fontSize: '14px', opacity: '0.9' }}>
              {filteredImages.length} images found
              {showCompetitors ? ' • All Competitors' : (searchQuery ? ` • Brand: "${searchQuery}"` : ' • All brands')}
              {state && ` • State: ${state}`}
            </div>
          </div>

          {/* Image Grid */}
          <div
            className="grid gap-4"
            style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',
              gap: '20px',
              padding: '0 20px 20px 20px'
            }}
          >
            {filteredImages.map((image, index) => (
              <div
                key={`${image.path}-${index}`}
                className="bg-white rounded-lg shadow-lg border overflow-hidden hover:shadow-xl transition-all duration-300 cursor-pointer transform hover:scale-105"
                onClick={() => openModal(image, index)}
                style={{
                  background: 'white',
                  borderRadius: '15px',
                  boxShadow: '0 8px 25px rgba(0,0,0,0.15)',
                  border: '1px solid #e0e0e0',
                  overflow: 'hidden',
                  transition: 'all 0.3s ease',
                  cursor: 'pointer'
                }}
              >
                {/* Image Container */}
                <div
                  className="relative overflow-hidden"
                  style={{
                    position: 'relative',
                    height: '200px',
                    overflow: 'hidden'
                  }}
                >
                  <img
                    src={`data:image/jpeg;base64,${image.base64}`}
                    alt={image.name}
                    className="w-full h-full object-cover"
                    style={{
                      width: '100%',
                      height: '100%',
                      objectFit: 'cover',
                      transition: 'transform 0.3s ease'
                    }}
                    loading="lazy"
                  />

                  {/* Hover Overlay */}
                  <div
                    className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center"
                    style={{
                      position: 'absolute',
                      top: '0',
                      left: '0',
                      right: '0',
                      bottom: '0',
                      background: 'rgba(0,0,0,0)',
                      transition: 'background 0.3s ease',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}
                  >
                    <Eye className="w-8 h-8 text-white opacity-0 hover:opacity-100 transition-opacity" />
                  </div>
                </div>

                {/* Image Info */}
                <div
                  className="p-4"
                  style={{ padding: '15px' }}
                >
                  {/* Brand Badge */}
                  <div className="flex items-center justify-between mb-3">
                    <span
                      className="text-sm font-bold text-white px-3 py-1 rounded-full"
                      style={{
                        background: 'linear-gradient(135deg, #ff6600, #ff8533)',
                        color: 'white',
                        fontSize: '12px',
                        fontWeight: 'bold',
                        padding: '4px 12px',
                        borderRadius: '20px'
                      }}
                    >
                      {image.brand || 'Unknown Brand'}
                    </span>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        downloadImage(image);
                      }}
                      className="text-gray-400 hover:text-orange-500 transition-colors"
                      style={{
                        background: 'none',
                        border: 'none',
                        cursor: 'pointer',
                        padding: '4px'
                      }}
                    >
                      <Download className="w-4 h-4" />
                    </button>
                  </div>

                  {/* Image Name */}
                  <h4
                    className="text-sm font-semibold text-gray-800 mb-2 truncate"
                    style={{
                      fontSize: '14px',
                      fontWeight: '600',
                      color: '#2d3748',
                      marginBottom: '8px',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap'
                    }}
                  >
                    {image.name}
                  </h4>

                  {/* Metadata */}
                  <div className="space-y-1 text-xs text-gray-500">
                    <div className="flex items-center">
                      <MapPin className="w-3 h-3 mr-1" />
                      <span>{image.state || 'Unknown State'}</span>
                    </div>
                    {image.coordinates && (
                      <div className="flex items-center">
                        <span className="w-3 h-3 mr-1">📍</span>
                        <span>{image.coordinates}</span>
                      </div>
                    )}
                    {image.location && (
                      <div className="flex items-center">
                        <span className="w-3 h-3 mr-1">📍</span>
                        <span>{image.location}</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      ) : (
        <div
          className="text-center py-12 bg-gray-50 rounded-lg border-2 border-dashed border-orange-300"
          style={{
            textAlign: 'center',
            padding: '40px',
            background: '#f8f9fa',
            borderRadius: '10px',
            margin: '20px 0',
            border: '2px dashed #ff6600'
          }}
        >
          <Camera className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No images found</h3>
          {showCompetitors ? (
            <div className="text-gray-500">
              <p className="mb-2">🔍 No competitor images found</p>
              <div className="text-sm">
                <p><strong>Try:</strong></p>
                <ul className="list-disc list-inside mt-2 space-y-1">
                  <li>Checking if images exist in the interactive_images folder</li>
                  <li>Verifying the selected state has available images</li>
                  <li>Ensuring image files are in supported formats (JPG, JPEG, PNG, GIF, BMP)</li>
                </ul>
              </div>
            </div>
          ) : searchQuery ? (
            <div className="text-gray-500">
              <p className="mb-2">No images found for brand "{searchQuery}"</p>
              <div className="text-sm">
                <p><strong>Try:</strong></p>
                <ul className="list-disc list-inside mt-2 space-y-1">
                  <li>Searching for different brand names (Amaron, Exide, Tata)</li>
                  <li>Enabling "Show All Competitors" to see all available images</li>
                  <li>Checking if images exist for the selected state and brand</li>
                </ul>
              </div>
            </div>
          ) : (
            <div className="text-gray-500">
              <p className="mb-2">📁 No images available in the interactive gallery</p>
              <div className="text-sm">
                <p><strong>Expected image location:</strong> interactive_images/ folder with brand-specific subfolders</p>
                <p><strong>Supported formats:</strong> JPG, JPEG, PNG, GIF, BMP</p>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Modal */}
      {selectedImage && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-4xl max-h-full overflow-hidden">
            <div className="flex items-center justify-between p-4 border-b">
              <div>
                <h3 className="text-lg font-semibold">{selectedImage.name}</h3>
                <p className="text-sm text-gray-600">{selectedImage.brand} - {selectedImage.state}</p>
              </div>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => downloadImage(selectedImage)}
                  className="p-2 text-gray-600 hover:text-gray-800 transition-colors"
                >
                  <Download className="w-5 h-5" />
                </button>
                <button
                  onClick={closeModal}
                  className="p-2 text-gray-600 hover:text-gray-800 transition-colors"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>
            </div>
            
            <div className="relative">
              <img
                src={`data:image/jpeg;base64,${selectedImage.base64}`}
                alt={selectedImage.name}
                className="max-w-full max-h-96 object-contain mx-auto"
              />
              
              {filteredImages.length > 1 && (
                <>
                  <button
                    onClick={() => navigateImage('prev')}
                    className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-75 transition-all"
                  >
                    <ChevronLeft className="w-6 h-6" />
                  </button>
                  <button
                    onClick={() => navigateImage('next')}
                    className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-75 transition-all"
                  >
                    <ChevronRight className="w-6 h-6" />
                  </button>
                </>
              )}
            </div>
            
            <div className="p-4 border-t bg-gray-50">
              <div className="flex items-center justify-between text-sm text-gray-600">
                <span>Image {currentImageIndex + 1} of {filteredImages.length}</span>
                <div className="flex items-center space-x-4">
                  <span className="flex items-center">
                    <Calendar className="w-4 h-4 mr-1" />
                    {selectedImage.timestamp}
                  </span>
                  <span className="flex items-center">
                    <MapPin className="w-4 h-4 mr-1" />
                    {selectedImage.state}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
