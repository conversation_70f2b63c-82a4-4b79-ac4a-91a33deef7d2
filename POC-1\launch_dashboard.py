#!/usr/bin/env python3
"""
Launch script for Yusuf <PERSON> Analytics Dashboard
This script helps start the React dashboard from the Streamlit app
"""

import subprocess
import os
import sys
import time
import webbrowser
from pathlib import Path

def launch_dashboard():
    """Launch the Yusuf Sarai Analytics Dashboard"""
    
    # Path to the React dashboard
    dashboard_path = Path("POC-1/sales-ys/sales-ys")
    
    if not dashboard_path.exists():
        print(f"❌ Dashboard path not found: {dashboard_path}")
        return False
    
    try:
        # Change to dashboard directory
        os.chdir(dashboard_path)
        
        # Check if node_modules exists
        if not Path("node_modules").exists():
            print("📦 Installing dependencies...")
            result = subprocess.run(["npm", "install"], capture_output=True, text=True)
            if result.returncode != 0:
                print(f"❌ Failed to install dependencies: {result.stderr}")
                return False
            print("✅ Dependencies installed successfully")
        
        # Start the development server
        print("🚀 Starting Yusuf Sarai Analytics Dashboard...")
        print("📍 Dashboard will be available at: http://localhost:5173")
        
        # Start the dev server in background
        process = subprocess.Popen(
            ["npm", "run", "dev"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # Wait a moment for server to start
        time.sleep(3)
        
        # Try to open in browser
        try:
            webbrowser.open("http://localhost:5173")
            print("🌐 Dashboard opened in browser")
        except Exception as e:
            print(f"⚠️  Could not open browser automatically: {e}")
            print("📱 Please manually open: http://localhost:5173")
        
        print("✅ Dashboard started successfully!")
        print("💡 Press Ctrl+C to stop the dashboard")
        
        # Keep the process running
        try:
            process.wait()
        except KeyboardInterrupt:
            print("\n🛑 Stopping dashboard...")
            process.terminate()
            print("✅ Dashboard stopped")
        
        return True
        
    except FileNotFoundError:
        print("❌ Node.js/npm not found. Please install Node.js first.")
        print("📥 Download from: https://nodejs.org/")
        return False
    except Exception as e:
        print(f"❌ Error starting dashboard: {e}")
        return False

if __name__ == "__main__":
    launch_dashboard()
