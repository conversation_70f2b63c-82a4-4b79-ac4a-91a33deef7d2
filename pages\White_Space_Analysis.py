import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import folium
try:
    from streamlit_folium import st_folium
    FOLIUM_AVAILABLE = True
except ImportError:
    FOLIUM_AVAILABLE = False
    st.warning("streamlit-folium not available. Map display will be limited.")
import sys
import os

# Add parent directory to path to import white_space_analysis
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from white_space_analysis import WhiteSpaceAnalyzer
    from business_potential_analyzer import BusinessPotentialAnalyzer
except ImportError as e:
    st.error(f"Import error: {e}")
    st.stop()

# Page configuration
st.set_page_config(
    page_title="White Space & Opportunity Analysis",
    page_icon="📊",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for styling
st.markdown("""
<style>
    .main-header {
        background: linear-gradient(90deg, #ff6600, #ff8533);
        padding: 1rem;
        border-radius: 10px;
        color: white;
        text-align: center;
        margin-bottom: 2rem;
    }
    
    .metric-card {
        background: white;
        padding: 1rem;
        border-radius: 8px;
        border-left: 4px solid #ff6600;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin-bottom: 1rem;
    }
    
    .opportunity-card {
        background: linear-gradient(145deg, #fff5f5, #ffe6e6);
        padding: 1.5rem;
        border-radius: 10px;
        border: 2px solid #dc143c;
        margin: 1rem 0;
    }
    
    .coverage-card {
        background: linear-gradient(145deg, #f0fff0, #e6ffe6);
        padding: 1.5rem;
        border-radius: 10px;
        border: 2px solid #2e8b57;
        margin: 1rem 0;
    }
    
    .recommendation-box {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 8px;
        border-left: 4px solid #007bff;
        margin: 0.5rem 0;
    }
    
    .priority-high {
        border-left-color: #dc143c !important;
    }
    
    .priority-medium {
        border-left-color: #ffa500 !important;
    }
</style>
""", unsafe_allow_html=True)

# Main header
st.markdown("""
<div class="main-header">
    <h1>📊 What are the white spaces and opportunity zones for retail growth?</h1>
    <h2>🚀 Business Potential Analysis</h2>
</div>
""", unsafe_allow_html=True)

# Initialize the analyzer
@st.cache_data
def load_analyzer():
    analyzer = WhiteSpaceAnalyzer()
    analyzer.load_retailer_data()
    analyzer.load_market_data()
    return analyzer

# Sidebar controls
with st.sidebar:
    st.header("🎯 Analysis Configuration")
    
    # Brand selection
    available_brands = ['amaron', 'exide', 'luminous', 'tata_green']
    selected_brand = st.selectbox(
        "Select Your Brand",
        available_brands,
        index=0,
        help="Choose the brand you want to analyze"
    )
    
    # Analysis type
    analysis_type = st.radio(
        "Analysis Focus",
        ["Complete Analysis", "Coverage Only", "Opportunities Only", "Market Potential"],
        help="Choose the type of analysis to perform"
    )
    
    # Geographic scope
    geographic_scope = st.selectbox(
        "Geographic Scope",
        ["Delhi NCR", "All Regions"],
        help="Select the geographic area for analysis"
    )
    
    st.markdown("---")
    
    # Action buttons
    if st.button("🔍 Run Analysis", type="primary", use_container_width=True):
        st.session_state.run_analysis = True
    
    if st.button("📊 Generate Report", use_container_width=True):
        st.session_state.generate_report = True
    
    if st.button("📥 Export Data", use_container_width=True):
        st.session_state.export_data = True

# Main content area
if st.session_state.get('run_analysis', False):
    
    # Load analyzer
    with st.spinner("Loading data and performing analysis..."):
        analyzer = load_analyzer()
        
        # Perform analysis
        coverage_analysis = analyzer.analyze_pin_coverage(selected_brand)
        potential_analysis = analyzer.calculate_market_potential(selected_brand)
        
    # Display results based on analysis type
    if analysis_type in ["Complete Analysis", "Coverage Only"]:
        
        # Coverage Overview Section
        st.markdown("## 📍 Current Market Coverage")
        
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.markdown(f"""
            <div class="metric-card">
                <h3 style="color: #2e8b57; margin: 0;">{coverage_analysis['target_brand_pins']}</h3>
                <p style="margin: 0; color: #666;">Areas Covered</p>
            </div>
            """, unsafe_allow_html=True)
        
        with col2:
            st.markdown(f"""
            <div class="metric-card">
                <h3 style="color: #ff6600; margin: 0;">{coverage_analysis['coverage_percentage']:.1f}%</h3>
                <p style="margin: 0; color: #666;">Market Coverage</p>
            </div>
            """, unsafe_allow_html=True)
        
        with col3:
            st.markdown(f"""
            <div class="metric-card">
                <h3 style="color: #dc143c; margin: 0;">{coverage_analysis['white_space_pins']}</h3>
                <p style="margin: 0; color: #666;">White Space Areas</p>
            </div>
            """, unsafe_allow_html=True)
        
        with col4:
            st.markdown(f"""
            <div class="metric-card">
                <h3 style="color: #007bff; margin: 0;">{coverage_analysis['total_market_pins']}</h3>
                <p style="margin: 0; color: #666;">Total Market Size</p>
            </div>
            """, unsafe_allow_html=True)
    
    # White Space Analysis Questions - Collapsible Sections
    st.markdown("---")
    st.markdown("## 🔍 White Space Analysis Questions")
    
    # Question 1: Which areas are not covered by my brand?
    with st.expander("📍 Which areas are not covered by my brand?", expanded=False):
        st.info("Content for uncovered areas analysis will be displayed here.")
        # Placeholder for future implementation
        st.write("This section will show detailed analysis of areas where your brand has no presence.")
    
    # Question 2: How much can I gain by covering these areas?
    with st.expander("💰 How much can I gain by covering these areas?", expanded=False):
        st.info("Content for revenue opportunity analysis will be displayed here.")
        # Placeholder for future implementation
        st.write("This section will show potential revenue gains from expanding into white space areas.")
    
    # Question 3: How much will it change net share for me?
    with st.expander("📈 How much will it change net share for me?", expanded=False):
        st.info("Content for market share impact analysis will be displayed here.")
        # Placeholder for future implementation
        st.write("This section will show the impact on your overall market share from expansion.")
    
    if analysis_type in ["Complete Analysis", "Opportunities Only"]:
        
        # White Space Opportunities Section
        st.markdown("## 🎯 White Space Opportunities")
        
        col1, col2 = st.columns([1, 1])
        
        with col1:
            st.markdown(f"""
            <div class="opportunity-card">
                <h3 style="color: #dc143c; margin-top: 0;">Immediate Opportunities</h3>
                <h2 style="color: #dc143c;">{coverage_analysis['white_space_pins']} Areas</h2>
                <p><strong>Market Share Potential:</strong> {coverage_analysis['white_space_percentage']:.1f}%</p>
                <p><strong>Competitor Presence:</strong> {coverage_analysis['competitor_pins']} locations</p>
            </div>
            """, unsafe_allow_html=True)
        
        with col2:
            st.markdown(f"""
            <div class="coverage-card">
                <h3 style="color: #2e8b57; margin-top: 0;">Current Strength</h3>
                <h2 style="color: #2e8b57;">{coverage_analysis['coverage_percentage']:.1f}% Coverage</h2>
                <p><strong>Established Presence:</strong> {coverage_analysis['target_brand_pins']} locations</p>
                <p><strong>Market Overlap:</strong> {coverage_analysis['overlap_pins']} shared areas</p>
            </div>
            """, unsafe_allow_html=True)
    
    if analysis_type in ["Complete Analysis", "Market Potential"]:
        
        # Market Potential Section
        st.markdown("## 💰 Revenue & Growth Potential")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.metric(
                "Estimated Revenue Opportunity",
                f"₹{potential_analysis['estimated_revenue_opportunity']:,.0f}",
                delta=f"+{potential_analysis['market_penetration_opportunity']:.1f}% market penetration"
            )
        
        with col2:
            st.metric(
                "Market Share Gain Potential",
                f"{potential_analysis['market_share_gain_potential']:.1f}%",
                delta=f"{potential_analysis['white_space_areas']} new areas"
            )
        
        with col3:
            st.metric(
                "Electronic Market Potential",
                f"{potential_analysis['electronic_market_potential']:.2f}",
                delta=f"Automotive: {potential_analysis['automotive_market_potential']:.2f}"
            )
    
    # Charts Section
    if analysis_type == "Complete Analysis":
        st.markdown("## 📊 Visual Analysis")
        
        # Generate charts
        fig_pie, fig_bar, fig_metrics, potential = analyzer.create_coverage_charts(selected_brand)
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.plotly_chart(fig_pie, use_container_width=True)
        
        with col2:
            st.plotly_chart(fig_bar, use_container_width=True)
        
        st.plotly_chart(fig_metrics, use_container_width=True)
    
    # Interactive Map Section
    if analysis_type in ["Complete Analysis", "Coverage Only"]:
        st.markdown("## 🗺️ Geographic Coverage Map")
        
        with st.spinner("Generating interactive map..."):
            coverage_map = analyzer.generate_coverage_map(selected_brand)
            
            if FOLIUM_AVAILABLE:
                st_folium(coverage_map, width=1200, height=600)
            else:
                st.info("Interactive map requires streamlit-folium package. Showing static map information instead.")
                st.write("Map would show coverage areas and white space opportunities.")

# Reset session state
if st.sidebar.button("🔄 Reset Analysis"):
    for key in ['run_analysis', 'generate_report', 'export_data']:
        if key in st.session_state:
            del st.session_state[key]
    st.rerun()
