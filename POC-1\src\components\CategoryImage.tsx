import React, { useState, useEffect } from 'react';
import { ZoomIn, ZoomOut, RotateCcw, Download } from 'lucide-react';

interface CategoryImageProps {
  subcategory: string;
  imageName: string;
  state: string;
  businessCategory: string;
  category: string;
}

export const CategoryImage: React.FC<CategoryImageProps> = ({ 
  subcategory, 
  imageName, 
  state,
  businessCategory,
  category
}) => {
  const [imageData, setImageData] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [zoomLevel, setZoomLevel] = useState<number>(100);

  useEffect(() => {
    loadCategoryImage();
  }, [imageName, state]);

  const loadCategoryImage = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch(`/api/category/image/${imageName}?state=${encodeURIComponent(state)}`);
      
      if (!response.ok) {
        throw new Error(`Failed to load image: ${response.statusText}`);
      }
      
      const data = await response.json();
      
      if (data.success && data.base64) {
        setImageData(data.base64);
      } else {
        throw new Error(data.error || 'Failed to load image');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load category image');
    } finally {
      setLoading(false);
    }
  };

  const handleZoom = (direction: 'in' | 'out' | 'reset') => {
    if (direction === 'reset') {
      setZoomLevel(100);
    } else if (direction === 'in' && zoomLevel < 300) {
      setZoomLevel(prev => Math.min(prev + 25, 300));
    } else if (direction === 'out' && zoomLevel > 25) {
      setZoomLevel(prev => Math.max(prev - 25, 25));
    }
  };

  const downloadImage = () => {
    if (imageData) {
      const link = document.createElement('a');
      link.href = `data:image/png;base64,${imageData}`;
      link.download = `${subcategory.replace(/\s+/g, '_')}_${state}.png`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96 bg-gray-50 rounded-lg">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading category image...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-96 bg-red-50 rounded-lg border border-red-200">
        <div className="text-center">
          <div className="text-red-500 text-4xl mb-4">⚠️</div>
          <h3 className="text-lg font-semibold text-red-800 mb-2">Image Not Available</h3>
          <p className="text-red-600 mb-4">{error}</p>
          <button
            onClick={loadCategoryImage}
            className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border">
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold text-gray-800">
            🛍️ {subcategory}
          </h2>
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-600">
              {state} | Zoom: {zoomLevel}%
            </span>
          </div>
        </div>
        <div className="mt-2 text-sm text-gray-600">
          <span className="font-medium">{businessCategory}</span> → <span className="font-medium">{category}</span> → <span className="text-orange-600">{subcategory}</span>
        </div>
      </div>

      {/* Image Container */}
      <div className="p-4">
        <div 
          className="overflow-auto border border-gray-200 rounded-lg bg-gray-50"
          style={{ maxHeight: '650px' }}
        >
          <div className="flex justify-center items-center min-h-96">
            <img
              src={`data:image/png;base64,${imageData}`}
              alt={subcategory}
              className="max-w-none transition-transform duration-200"
              style={{ 
                transform: `scale(${zoomLevel / 100})`,
                transformOrigin: 'center'
              }}
            />
          </div>
        </div>
      </div>

      {/* Zoom Controls */}
      <div className="p-4 border-t border-gray-200 bg-gray-50">
        <div className="flex items-center justify-center space-x-4">
          <button
            onClick={() => handleZoom('out')}
            disabled={zoomLevel <= 25}
            className="flex items-center px-3 py-2 bg-white border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <ZoomOut className="w-4 h-4 mr-1" />
            Zoom Out
          </button>
          
          <div className="flex items-center space-x-2">
            <input
              type="range"
              min="25"
              max="300"
              step="25"
              value={zoomLevel}
              onChange={(e) => setZoomLevel(Number(e.target.value))}
              className="w-32"
            />
            <span className="text-sm font-medium text-gray-700 w-12">
              {zoomLevel}%
            </span>
          </div>
          
          <button
            onClick={() => handleZoom('in')}
            disabled={zoomLevel >= 300}
            className="flex items-center px-3 py-2 bg-white border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <ZoomIn className="w-4 h-4 mr-1" />
            Zoom In
          </button>
          
          <button
            onClick={() => handleZoom('reset')}
            className="flex items-center px-3 py-2 bg-white border border-gray-300 rounded hover:bg-gray-50 transition-colors"
          >
            <RotateCcw className="w-4 h-4 mr-1" />
            Reset
          </button>
          
          <button
            onClick={downloadImage}
            className="flex items-center px-3 py-2 bg-orange-500 text-white rounded hover:bg-orange-600 transition-colors"
          >
            <Download className="w-4 h-4 mr-1" />
            Download
          </button>
        </div>
      </div>
    </div>
  );
};
