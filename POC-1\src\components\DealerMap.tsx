import React, { useEffect, useRef, useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, LayersControl, ZoomControl } from 'react-leaflet';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';
import { MapPin, Loader, AlertCircle, Filter, Search, Map as MapIcon, Layers, ZoomIn, ZoomOut } from 'lucide-react';

// Fix for default markers in react-leaflet
delete (L.Icon.Default.prototype as any)._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

interface Dealer {
  name: string;
  pin: string;
  brand: string;
  color: string;
  lat: number;
  lon: number;
  image?: string;
  address?: string;
}

interface DealerMapProps {
  dealers?: Dealer[];
  onDealersLoad?: (dealers: Dealer[]) => void;
  center?: [number, number];
  zoom?: number;
  height?: string;
}

export const DealerMap: React.FC<DealerMapProps> = ({
  dealers: propDealers,
  onDealersLoad,
  center = [28.6139, 77.2090], // Delhi center
  zoom = 11,
  height = '600px'
}) => {
  const [dealers, setDealers] = useState<Dealer[]>(propDealers || []);
  const [loading, setLoading] = useState(!propDealers);
  const [error, setError] = useState<string | null>(null);
  const [filteredDealers, setFilteredDealers] = useState<Dealer[]>([]);
  const [selectedBrands, setSelectedBrands] = useState<string[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedState, setSelectedState] = useState('Delhi(NCT)');
  const [selectedGeography, setSelectedGeography] = useState('South Central');
  const [selectedLocality, setSelectedLocality] = useState('Yusuf Sarai');
  const [showCompetitors, setShowCompetitors] = useState(false);
  const [showStrategicReport, setShowStrategicReport] = useState(false);
  const mapRef = useRef<L.Map | null>(null);

  // Exact data from Streamlit app
  const states = ['Delhi(NCT)', 'West Bengal', 'Maharashtra', 'Karnataka', 'Tamil Nadu'];
  const geographies: { [key: string]: string[] } = {
    'Delhi(NCT)': ['South Central', 'North', 'East', 'West'],
    'West Bengal': ['East', 'North', 'South', 'Central']
  };
  const localities: { [key: string]: { [key: string]: string[] } } = {
    'Delhi(NCT)': {
      'South Central': ['Yusuf Sarai', 'Green Park', 'Hauz Khas', 'Safdarjung'],
      'North': ['Karol Bagh', 'Rajouri Garden', 'Pitampura'],
      'East': ['Laxmi Nagar', 'Preet Vihar', 'Mayur Vihar'],
      'West': ['Janakpuri', 'Dwarka', 'Uttam Nagar']
    },
    'West Bengal': {
      'East': ['South 24 Parganas', 'North 24 Parganas', 'Howrah'],
      'North': ['Darjeeling', 'Jalpaiguri', 'Cooch Behar'],
      'South': ['Midnapore', 'Purulia', 'Bankura'],
      'Central': ['Kolkata', 'Hooghly', 'Nadia']
    }
  };

  // Custom icons for different brands
  const createCustomIcon = (color: string, brand: string) => {
    const iconHtml = `
      <div style="
        background-color: ${getColorCode(color)};
        width: 25px;
        height: 25px;
        border-radius: 50%;
        border: 3px solid white;
        box-shadow: 0 2px 4px rgba(0,0,0,0.3);
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        color: white;
        font-size: 12px;
      ">
        ${brand.charAt(0)}
      </div>
    `;
    
    return L.divIcon({
      html: iconHtml,
      className: 'custom-dealer-icon',
      iconSize: [25, 25],
      iconAnchor: [12, 12],
      popupAnchor: [0, -12]
    });
  };

  const getColorCode = (color: string): string => {
    const colorMap: { [key: string]: string } = {
      red: '#ef4444',
      blue: '#3b82f6',
      green: '#10b981',
      yellow: '#f59e0b',
      orange: '#f97316',
      purple: '#8b5cf6'
    };
    return colorMap[color] || '#6b7280';
  };

  // Load dealers from API
  useEffect(() => {
    if (!propDealers) {
      loadDealers();
    }
  }, [propDealers]);

  // Filter dealers based on search and brand selection
  useEffect(() => {
    let filtered = dealers;

    // Filter by selected brands
    if (selectedBrands.length > 0) {
      filtered = filtered.filter(dealer => selectedBrands.includes(dealer.brand));
    }

    // Filter by search query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(dealer =>
        dealer.name.toLowerCase().includes(query) ||
        dealer.brand.toLowerCase().includes(query) ||
        dealer.pin.includes(query) ||
        dealer.address?.toLowerCase().includes(query)
      );
    }

    setFilteredDealers(filtered);
  }, [dealers, selectedBrands, searchQuery]);

  const loadDealers = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch('/api/map/dealers');
      if (!response.ok) {
        throw new Error('Failed to load dealers');
      }
      
      const data = await response.json();
      setDealers(data.dealers || []);
      onDealersLoad?.(data.dealers || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load dealers');
    } finally {
      setLoading(false);
    }
  };

  const handleBrandToggle = (brand: string) => {
    setSelectedBrands(prev =>
      prev.includes(brand)
        ? prev.filter(b => b !== brand)
        : [...prev, brand]
    );
  };

  const uniqueBrands = Array.from(new Set(dealers.map(d => d.brand)));

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96 bg-gray-50 rounded-lg">
        <div className="text-center">
          <Loader className="w-8 h-8 animate-spin mx-auto mb-2 text-orange-500" />
          <p className="text-gray-600">Loading dealer locations...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-96 bg-red-50 rounded-lg border border-red-200">
        <div className="text-center">
          <AlertCircle className="w-8 h-8 mx-auto mb-2 text-red-500" />
          <p className="text-red-600 font-medium">Error loading map</p>
          <p className="text-red-500 text-sm">{error}</p>
          <button
            onClick={loadDealers}
            className="mt-3 px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Controls */}
      <div className="bg-white p-4 rounded-lg shadow-sm border">
        <div className="flex flex-col md:flex-row gap-4">
          {/* Search */}
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="Search dealers by name, brand, PIN, or address..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 outline-none"
              />
            </div>
          </div>

          {/* Brand Filters */}
          <div className="flex items-center gap-2">
            <Filter className="w-5 h-5 text-gray-500" />
            <span className="text-sm font-medium text-gray-700">Brands:</span>
            {uniqueBrands.map(brand => (
              <button
                key={brand}
                onClick={() => handleBrandToggle(brand)}
                className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                  selectedBrands.includes(brand) || selectedBrands.length === 0
                    ? 'bg-orange-500 text-white'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                {brand}
              </button>
            ))}
          </div>
        </div>

        {/* Stats */}
        <div className="mt-3 flex items-center gap-4 text-sm text-gray-600">
          <span>Total Dealers: {dealers.length}</span>
          <span>Showing: {filteredDealers.length}</span>
          {uniqueBrands.map(brand => (
            <span key={brand}>
              {brand}: {dealers.filter(d => d.brand === brand).length}
            </span>
          ))}
        </div>
      </div>

      {/* Map */}
      <div className="bg-white rounded-lg shadow-sm border overflow-hidden" style={{ height }}>
        <MapContainer
          center={center}
          zoom={zoom}
          style={{ height: '100%', width: '100%' }}
          ref={mapRef}
        >
          <LayersControl position="topright">
            <LayersControl.BaseLayer checked name="OpenStreetMap">
              <TileLayer
                url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
              />
            </LayersControl.BaseLayer>
            
            <LayersControl.BaseLayer name="Satellite">
              <TileLayer
                url="https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}"
                attribution='&copy; <a href="https://www.esri.com/">Esri</a>'
              />
            </LayersControl.BaseLayer>
          </LayersControl>

          {filteredDealers.map((dealer, index) => (
            <Marker
              key={`${dealer.name}-${dealer.pin}-${index}`}
              position={[dealer.lat, dealer.lon]}
              icon={createCustomIcon(dealer.color, dealer.brand)}
            >
              <Popup maxWidth={300} className="dealer-popup">
                <div className="p-2">
                  <div className="flex items-center gap-2 mb-2">
                    <div
                      className="w-4 h-4 rounded-full"
                      style={{ backgroundColor: getColorCode(dealer.color) }}
                    />
                    <h3 className="font-semibold text-lg">{dealer.brand} Dealer</h3>
                  </div>
                  
                  <div className="space-y-1 text-sm">
                    <p><strong>Name:</strong> {dealer.name}</p>
                    <p><strong>PIN:</strong> {dealer.pin}</p>
                    {dealer.address && (
                      <p><strong>Address:</strong> {dealer.address}</p>
                    )}
                  </div>

                  {dealer.image && (
                    <div className="mt-3">
                      <img
                        src={`/images/${dealer.image}`}
                        alt={`${dealer.brand} dealer`}
                        className="w-full h-32 object-cover rounded"
                        onError={(e) => {
                          (e.target as HTMLImageElement).style.display = 'none';
                        }}
                      />
                    </div>
                  )}
                </div>
              </Popup>
            </Marker>
          ))}
        </MapContainer>
      </div>
    </div>
  );
};
