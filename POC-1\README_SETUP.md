# POC-1 Project Setup Guide

## 🚀 Quick Setup (After Importing from Another Computer)

### Option 1: Automated Setup (Recommended)
```bash
# Run the automated setup
python setup_and_check.py

# Or use the batch file on Windows
setup.bat
```

### Option 2: Manual Setup

#### 1. Install Dependencies
```bash
# Install all required packages
pip install -r requirements.txt

# Or install individually
pip install streamlit>=1.45.1
pip install pandas>=1.5.0
pip install pillow>=9.0.0
pip install folium>=0.19.0
pip install geopy>=2.4.0
pip install openai>=1.0.0
pip install plotly>=5.0.0
pip install openpyxl>=3.0.0
```

#### 2. Set Environment Variables (Optional)
```bash
# For OpenAI API (if using AI features)
set OPENAI_API_KEY=your-api-key-here
```

#### 3. Verify Setup
```bash
python setup_and_check.py
```

## 🗂️ Required Files and Directories

### Critical Files:
- ✅ `StonesburryMimoAnalyticsDashboard.py` - Main dashboard
- ✅ `auth.py` - Authentication module
- ✅ `login.html` - Login page
- ✅ `requirements.txt` - Python dependencies
- ✅ `amaron_retailer_image.csv` - Amaron retailer data
- ✅ `exide_retailer_image.csv` - Exide retailer data
- ✅ `tata_green_retailer_image.csv` - Tata retailer data
- ✅ `luminous_retailer_image.csv` - Luminous retailer data
- ✅ `YusufSaraiAmaronCentre-FinalCSV.xlsx` - Main Excel data

### Required Directories:
- ✅ `GeoIQimages/` - GeoIQ analysis images
- ✅ `Items/` - Item price images
- ✅ `images/` - General images
- ✅ `logo/` - Logo files
- ✅ `pages/` - Streamlit pages
- ✅ `Centres/` - Centre data files
- ✅ `interactive_images/` - Interactive map images

## 🚀 Running the Applications

### Main Dashboard
```bash
streamlit run StonesburryMimoAnalyticsDashboard.py
```

### Alternative Apps
```bash
# Test app
streamlit run app.py

# Amaron dashboard
streamlit run dashboard_amaron.py
```

### Using the Startup Script
```bash
python start_app.py
```

## 🔧 Troubleshooting

### Common Issues:

#### 1. Missing Dependencies
```bash
# Error: ModuleNotFoundError
# Solution: Install missing packages
pip install [package-name]
```

#### 2. File Not Found Errors
```bash
# Error: FileNotFoundError
# Solution: Check if files exist in correct locations
python setup_and_check.py
```

#### 3. Path Issues
- Ensure you're running commands from the POC-1 directory
- Check that all data files are in the correct locations

#### 4. OpenAI API Issues
- Set your API key as environment variable
- Or update the hardcoded key in app.py (not recommended for production)

### File Location Issues:
If you get file not found errors, the setup script will help identify missing files:

```bash
python setup_and_check.py
```

## 📁 Project Structure
```
POC-1/
├── StonesburryMimoAnalyticsDashboard.py  # Main dashboard
├── app.py                                # Test app
├── auth.py                              # Authentication
├── requirements.txt                     # Dependencies
├── setup_and_check.py                  # Setup script
├── login.html                           # Login page
├── GeoIQimages/                         # Analysis images
│   ├── YusufSaraiGEOIQImages/          # Delhi images
│   └── South24ParganasGEOIQImages/     # West Bengal images
├── Items/                               # Item price images
├── Centres/                             # Data files
│   ├── Delhi/                          # Delhi data
│   └── West bengal/                    # West Bengal data
├── pages/                               # Streamlit pages
├── images/                              # General images
└── logo/                                # Logo files
```

## 🎯 Next Steps

1. Run the setup script to verify everything is working
2. Start with the main dashboard: `streamlit run StonesburryMimoAnalyticsDashboard.py`
3. Access the login page first, then navigate to the dashboard
4. Test different features and data views

## 📞 Support

If you encounter issues:
1. Run `python setup_and_check.py` to diagnose problems
2. Check that all required files are present
3. Verify Python dependencies are installed
4. Ensure you're in the correct directory when running commands
