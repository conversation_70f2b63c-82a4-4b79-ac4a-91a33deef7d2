import React, { useState } from 'react';

interface LoginFormProps {
  onLogin: (email: string, password: string) => Promise<void>;
  isLoading?: boolean;
}

export const LoginForm: React.FC<LoginFormProps> = ({ onLogin, isLoading = false }) => {
  console.log('LoginForm rendering...');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!email.trim() || !password.trim()) {
      alert('Please fill in both fields.');
      return;
    }

    setLoading(true);

    try {
      await onLogin(email.trim(), password.trim());
    } catch (error) {
      alert('Invalid credentials. Please try again.');
      console.error('Login failed:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col md:flex-row relative">
      {/* Left Image Side - Exact match with login.html */}
      <div
        className="hidden md:flex w-full md:w-1/2 h-screen bg-cover bg-center relative"
        style={{ backgroundImage: "url('log.png')" }}
      >
        {/* Orange Overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-orange-500 via-orange-600 to-orange-700 opacity-10"></div>

        {/* Bottom Text */}
        <div className="absolute bottom-0 p-10 text-white z-10">
          <h3 className="text-2xl font-bold mb-1">Bring your ideas to life.</h3>
          <p className="text-sm">Sign up for free and enjoy access to all features for 30 days. No credit card required.</p>
        </div>
      </div>

      {/* Right Side: Form Section - Exact match with login.html */}
      <div className="w-full md:w-1/2 bg-white relative flex flex-col items-center justify-center px-8 py-12">

        {/* Top Bar: Stonesbury (Left) and Adobe (Right) */}
        <div className="absolute top-6 left-6 right-6 z-50 flex justify-between items-center px-6">
          {/* Stonesbury Logo (Left) */}
          <img src="logo/Stonesbury-logo.png" alt="Stonesbury Logo" className="h-14 w-auto" />

          {/* Adobe Logo (Right) */}
          <img src="logo/Adobe Express - file.png" alt="Adobe Logo" className="h-14 w-auto" />
        </div>

        {/* Login Form Box */}
        <div className="w-full max-w-md mt-20">
          <h2 className="text-3xl font-bold text-gray-900 mb-2">Welcome!</h2>
          <p className="text-sm text-gray-600 mb-6">Please enter your details</p>

          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-700">Email address</label>
              <input
                type="email"
                name="email"
                required
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="mt-1 w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 outline-none"
                placeholder="<EMAIL>"
              />
            </div>
            <div>
              <label className="text-sm font-medium text-gray-700">Password</label>
              <input
                type="password"
                name="password"
                required
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="mt-1 w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 outline-none"
                placeholder="••••••••"
              />
            </div>

            <button
              type="submit"
              disabled={loading || isLoading}
              className="w-full bg-orange-500 text-white py-2 rounded-md hover:bg-orange-600 transition-all flex items-center justify-center"
            >
              <span>{loading || isLoading ? 'Signing in...' : 'Sign in'}</span>
              {(loading || isLoading) && (
                <div className="ml-2">
                  <svg className="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                </div>
              )}
            </button>
          </form>
        </div>
      </div>
    </div>
  );
};
