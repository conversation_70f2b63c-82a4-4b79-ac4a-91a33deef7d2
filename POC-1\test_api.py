#!/usr/bin/env python3
"""
Test script to verify Flask API endpoints are working
"""

import requests
import json
import os

def test_flask_api():
    """Test Flask API endpoints"""
    base_url = "http://localhost:5000"
    
    print("🧪 Testing Flask API Endpoints")
    print("=" * 50)
    
    # Test 1: Basic health check
    try:
        response = requests.get(f"{base_url}/api/test/category/ExhaustFans.png", timeout=5)
        print(f"✅ Health check: {response.status_code}")
        if response.status_code == 200:
            print(f"   Response: {response.json()}")
    except Exception as e:
        print(f"❌ Health check failed: {e}")
        return False
    
    # Test 2: Category image endpoint
    try:
        params = {
            'state': 'Delhi(NCT)',
            'type': 'category'
        }
        response = requests.get(f"{base_url}/api/analytics/image/ExhaustFans.png", params=params, timeout=10)
        print(f"✅ Category image API: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success') and data.get('base64'):
                print(f"   ✅ Image loaded successfully")
                print(f"   📁 Path: {data.get('path', 'N/A')}")
                print(f"   📏 Base64 length: {len(data.get('base64', ''))}")
            else:
                print(f"   ❌ Image data missing: {data}")
        else:
            print(f"   ❌ Error: {response.text}")
            
    except Exception as e:
        print(f"❌ Category image test failed: {e}")
    
    # Test 3: Check if image files exist
    print("\n📁 Checking image files:")
    image_paths = [
        "Items/YusufSaraiItemPrice/ExhaustFans.png",
        "Items/South24ParganasItemPrices/ExhaustFans.png"
    ]
    
    for path in image_paths:
        if os.path.exists(path):
            size = os.path.getsize(path)
            print(f"   ✅ {path} ({size} bytes)")
        else:
            print(f"   ❌ {path} (missing)")
    
    print("\n🔧 API Test Complete!")
    return True

if __name__ == "__main__":
    test_flask_api()
