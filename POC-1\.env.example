# Flask Backend Configuration
FLASK_ENV=development
FLASK_DEBUG=1
SECRET_KEY=your-secret-key-change-this-in-production

# API Configuration
VITE_API_URL=http://localhost:5000/api

# Optional: OpenAI API Key for AI features
OPENAI_API_KEY=your-openai-api-key-here

# Database Configuration (if using database instead of files)
# DATABASE_URL=sqlite:///poc1.db

# CORS Configuration
CORS_ORIGINS=http://localhost:3000,http://localhost:5173

# File Upload Configuration
MAX_CONTENT_LENGTH=16777216  # 16MB
UPLOAD_FOLDER=uploads

# Geocoding Configuration
GEOCODING_USER_AGENT=poc1-dealer-mapper
GEOCODING_RATE_LIMIT=1  # seconds between requests
