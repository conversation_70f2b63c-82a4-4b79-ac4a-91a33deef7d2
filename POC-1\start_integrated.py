#!/usr/bin/env python3
"""
Unified startup script for POC-1 Integrated Dashboard
Starts both Flask backend and React frontend
"""

import os
import sys
import subprocess
import time
import threading
import signal
from pathlib import Path

class IntegratedDashboardStarter:
    def __init__(self):
        self.flask_process = None
        self.react_process = None
        self.running = True
        self.npm_command = 'npm'  # Default npm command

    def check_python_requirements(self):
        """Check if Python requirements are installed"""
        try:
            import flask
            import flask_cors
            import pandas
            import folium
            import geopy
            from PIL import Image
            print("✅ Python requirements are installed")
            return True
        except ImportError as e:
            print(f"❌ Missing Python requirement: {e}")
            return False

    def check_node_requirements(self):
        """Check if Node.js and npm are available"""
        try:
            # Check Node.js
            result = subprocess.run(['node', '--version'], capture_output=True, text=True, shell=True)
            if result.returncode == 0:
                print(f"✅ Node.js version: {result.stdout.strip()}")
                node_available = True
            else:
                print("❌ Node.js not found")
                node_available = False

            # Check npm with shell=True for Windows compatibility
            npm_commands = ['npm', 'npm.cmd']
            npm_available = False

            for npm_cmd in npm_commands:
                try:
                    result = subprocess.run([npm_cmd, '--version'], capture_output=True, text=True, shell=True)
                    if result.returncode == 0:
                        print(f"✅ npm version: {result.stdout.strip()}")
                        npm_available = True
                        self.npm_command = npm_cmd
                        break
                except:
                    continue

            if not npm_available:
                print("❌ npm not found")
                if node_available:
                    print("💡 Node.js is installed but npm is not accessible.")
                    print("   Try one of these solutions:")
                    print("   1. Restart your terminal/command prompt")
                    print("   2. Add Node.js to your PATH environment variable")
                    print("   3. Use Node.js installer to repair the installation")
                    print("   4. Run: npm install -g npm@latest")
                return False

            return node_available and npm_available
        except FileNotFoundError:
            print("❌ Node.js or npm not found in PATH")
            print("💡 Please install Node.js from https://nodejs.org/")
            return False

    def install_python_requirements(self):
        """Install Python requirements"""
        print("📦 Installing Python requirements...")
        try:
            subprocess.run([
                sys.executable, "-m", "pip", "install", "-r", "flask_requirements.txt"
            ], check=True)
            print("✅ Python requirements installed")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install Python requirements: {e}")
            return False

    def install_node_requirements(self):
        """Install Node.js requirements"""
        print("📦 Installing Node.js requirements...")
        try:
            subprocess.run([self.npm_command, 'install'], check=True, shell=True)
            print("✅ Node.js requirements installed")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install Node.js requirements: {e}")
            return False

    def check_data_files(self):
        """Check if required data files exist"""
        required_files = [
            "amaron_retailer_image.csv",
            "exide_retailer_image.csv", 
            "tata_green_retailer_image.csv",
            "luminous_retailer_image.csv",
            "DataValues-Dashboard-CSV.csv"
        ]
        
        missing_files = []
        for file in required_files:
            if not os.path.exists(file):
                missing_files.append(file)
        
        if missing_files:
            print("⚠️  Warning: Some data files are missing:")
            for file in missing_files:
                print(f"   - {file}")
            print("   The application will still work but some features may be limited.")
        else:
            print("✅ All required data files found")
        
        return len(missing_files) == 0

    def start_flask_backend(self):
        """Start Flask backend in a separate thread"""
        def run_flask():
            try:
                print("🚀 Starting Flask backend server on http://localhost:5000")
                os.environ['FLASK_ENV'] = 'development'
                os.environ['FLASK_DEBUG'] = '1'
                
                self.flask_process = subprocess.Popen([
                    sys.executable, "flask_backend.py"
                ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
                
                # Wait for process to complete or be terminated
                self.flask_process.wait()
                
            except Exception as e:
                print(f"❌ Flask backend error: {e}")

        flask_thread = threading.Thread(target=run_flask, daemon=True)
        flask_thread.start()
        
        # Wait a moment for Flask to start
        time.sleep(3)
        return flask_thread

    def start_react_frontend(self):
        """Start React frontend in a separate thread"""
        def run_react():
            try:
                print("🚀 Starting React frontend server on http://localhost:3000")

                self.react_process = subprocess.Popen([
                    self.npm_command, 'run', 'dev'
                ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, shell=True)

                # Wait for process to complete or be terminated
                self.react_process.wait()

            except Exception as e:
                print(f"❌ React frontend error: {e}")

        react_thread = threading.Thread(target=run_react, daemon=True)
        react_thread.start()

        # Wait a moment for React to start
        time.sleep(5)
        return react_thread

    def signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        print("\n🛑 Shutting down servers...")
        self.running = False
        
        if self.flask_process:
            self.flask_process.terminate()
            
        if self.react_process:
            self.react_process.terminate()
            
        sys.exit(0)

    def wait_for_servers(self):
        """Wait for servers to be ready"""
        print("⏳ Waiting for servers to start...")
        
        # Check Flask backend
        flask_ready = False
        for i in range(30):  # Wait up to 30 seconds
            try:
                import requests
                response = requests.get('http://localhost:5000/api/health', timeout=1)
                if response.status_code == 200:
                    flask_ready = True
                    print("✅ Flask backend is ready")
                    break
            except:
                pass
            time.sleep(1)
        
        if not flask_ready:
            print("⚠️  Flask backend may not be ready yet")
        
        # Check React frontend
        react_ready = False
        for i in range(30):  # Wait up to 30 seconds
            try:
                import requests
                response = requests.get('http://localhost:3000', timeout=1)
                if response.status_code == 200:
                    react_ready = True
                    print("✅ React frontend is ready")
                    break
            except:
                pass
            time.sleep(1)
        
        if not react_ready:
            print("⚠️  React frontend may not be ready yet")

    def run(self):
        """Main run method"""
        print("🔧 POC-1 Integrated Dashboard Startup")
        print("=" * 50)
        
        # Check if we're in the right directory
        if not os.path.exists("flask_backend.py") or not os.path.exists("package.json"):
            print("❌ Required files not found!")
            print("   Please run this script from the POC-1 directory")
            print("   Make sure both flask_backend.py and package.json exist")
            sys.exit(1)
        
        # Setup signal handlers
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
        # Check requirements
        print("\n📋 Checking requirements...")
        
        python_ok = self.check_python_requirements()
        node_ok = self.check_node_requirements()
        
        if not python_ok:
            print("📦 Installing Python requirements...")
            if not self.install_python_requirements():
                print("❌ Failed to install Python requirements")
                sys.exit(1)
        
        if not node_ok:
            print("❌ Node.js/npm not available. Please install Node.js first.")
            sys.exit(1)
        
        # Install Node.js dependencies
        if not os.path.exists("node_modules"):
            if not self.install_node_requirements():
                print("❌ Failed to install Node.js requirements")
                sys.exit(1)
        
        # Check data files
        self.check_data_files()
        
        # Create necessary directories
        os.makedirs("submitted_forms", exist_ok=True)
        print("✅ Created necessary directories")
        
        print("\n🚀 Starting integrated dashboard...")
        print("=" * 50)
        
        # Start both servers
        flask_thread = self.start_flask_backend()
        react_thread = self.start_react_frontend()
        
        # Wait for servers to be ready
        self.wait_for_servers()
        
        print("\n" + "=" * 50)
        print("🎉 POC-1 Integrated Dashboard is running!")
        print("=" * 50)
        print("📍 Frontend: http://localhost:3000")
        print("📍 Backend API: http://localhost:5000")
        print("📋 API Documentation: http://localhost:5000/api/health")
        print("\n🔐 Login Credentials:")
        print("   Email: <EMAIL>")
        print("   Password: user")
        print("\n" + "=" * 50)
        print("Press Ctrl+C to stop all servers")
        print("=" * 50)
        
        try:
            # Keep the main thread alive
            while self.running:
                time.sleep(1)
        except KeyboardInterrupt:
            self.signal_handler(signal.SIGINT, None)

def main():
    """Main entry point"""
    starter = IntegratedDashboardStarter()
    starter.run()

if __name__ == "__main__":
    main()
