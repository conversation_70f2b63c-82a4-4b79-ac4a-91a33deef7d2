import pandas as pd
import folium
from geopy.geocoders import Nominatim
from time import sleep

# Load dealer files
amaron_df = pd.read_csv('amaron_retailer_image.csv')
exide_df = pd.read_csv('exide_retailer_image.csv')
tata_df = pd.read_csv('tata_green_retailer_image.csv')

# Geolocator setup
geolocator = Nominatim(user_agent="dealer-mapper")

def get_lat_lon(pincode):
    try:
        location = geolocator.geocode(f"{pincode}, Delhi, India")
        location_path = geolocator.geocode_path(f"{pincode}, Delhi, India")
        if location:
            return pd.Series([location.latitude, location.longitude])
    except:
        pass
    return pd.Series([None, None])

def add_coordinates(df):
    coords = df['PIN'].apply(get_lat_lon)
    df['lat'], df['lon'] = coords[0], coords[1]
    sleep(1)  # Nominatim rate limiting
    return df.dropna(subset=['lat', 'lon'])

amaron_df = add_coordinates(amaron_df)
exide_df = add_coordinates(exide_df)
tata_df = add_coordinates(tata_df)

# Center map on Delhi
delhi_center = [28.6139, 77.2090]
map_delhi = folium.Map(location=delhi_center, zoom_start=11)

# Load optional GeoJSON


folium.LayerControl().add_to(map_delhi)

# Function to add markers with image popup
def add_pin_markers(df, color, name):
    for _, row in df.iterrows():
        image_html = f"""
            <b>{name} Dealer:</b> {row['Name']}<br>
            <b>PIN:</b> {row['PIN']}<br>
            <img src='images/{row["image"]}' width='200' height='150'>
        """
        folium.Marker(
            location=(row['lat'], row['lon']),
            popup=folium.Popup(image_html, max_width=250),
            icon=folium.Icon(color=color, icon='info-sign')
        ).add_to(map_delhi)

# Add pins
add_pin_markers(amaron_df, 'red', 'Amaron')
add_pin_markers(exide_df, 'blue', 'Exide')
add_pin_markers(tata_df, 'green', 'Tata Green')

# Save output
map_delhi.save('delhi_dealer_district_map_with_images.html')
print("Map with images saved as 'delhi_dealer_district_map_with_images.html'")
