# POC-1 Integrated Dashboard

A comprehensive analytics dashboard combining Flask backend API with React frontend for dealer mapping, image analysis, and form management.

## 🚀 Quick Start

### One-Command Setup
```bash
python start_integrated.py
```

This will:
- Install all Python and Node.js dependencies
- Start Flask backend on http://localhost:5000
- Start React frontend on http://localhost:3000
- Open your browser automatically

### Manual Setup
```bash
# Install Python dependencies
pip install -r flask_requirements.txt

# Install Node.js dependencies
npm install

# Start backend (Terminal 1)
python flask_backend.py

# Start frontend (Terminal 2)
npm run dev
```

## 📋 Prerequisites

- **Python 3.8+** with pip
- **Node.js 16+** with npm
- **Git** (for version control)

## 🏗️ Project Structure

```
POC-1/
├── 🔧 Backend (Flask)
│   ├── flask_backend.py          # Main Flask API server
│   ├── flask_requirements.txt    # Python dependencies
│   ├── start_flask.py            # Backend-only startup
│   └── auth.py                   # Authentication utilities
│
├── ⚛️ Frontend (React + TypeScript)
│   ├── src/
│   │   ├── components/           # React components
│   │   │   ├── AuthProvider.tsx  # Authentication context
│   │   │   ├── LoginForm.tsx     # Login interface
│   │   │   ├── Dashboard.tsx     # Main dashboard
│   │   │   ├── DealerMap.tsx     # Interactive maps
│   │   │   ├── ImageGallery.tsx  # Image browser
│   │   │   ├── MimoForm.tsx      # KPI validation form
│   │   │   └── ProtectedRoute.tsx # Route protection
│   │   ├── services/
│   │   │   └── api.ts            # API service layer
│   │   ├── App.tsx               # Main app component
│   │   ├── main.tsx              # Entry point
│   │   └── index.css             # Global styles
│   ├── package.json              # Node.js dependencies
│   ├── vite.config.ts            # Vite configuration
│   ├── tailwind.config.js        # Tailwind CSS config
│   └── index.html                # HTML template
│
├── 📊 Data Files
│   ├── amaron_retailer_image.csv
│   ├── exide_retailer_image.csv
│   ├── tata_green_retailer_image.csv
│   ├── luminous_retailer_image.csv
│   ├── DataValues-Dashboard-CSV.csv
│   ├── images/                   # Dealer photos
│   └── interactive_images/       # Gallery images
│
├── 🚀 Startup Scripts
│   ├── start_integrated.py       # Unified startup (recommended)
│   └── start_flask.py            # Backend-only startup
│
└── 📝 Documentation
    ├── README.md                 # This file
    ├── README_FLASK_BACKEND.md   # Backend API docs
    └── .env.example              # Environment variables
```

## 🎯 Features

### 🔐 Authentication System
- Session-based login/logout
- Protected routes and API endpoints
- Demo credentials: `<EMAIL>` / `user`

### 🗺️ Interactive Dealer Mapping
- Real-time geocoding of PIN codes
- Multi-brand dealer visualization
- Custom markers and popups
- Search and filtering capabilities
- Leaflet-based interactive maps

### 🖼️ Image Gallery Management
- State-wise image organization
- Brand filtering and search
- Modal view with navigation
- Download functionality
- Base64 image encoding

### 📝 KPI Validation Forms
- Comprehensive form validation
- Ticket generation system
- File-based storage
- Success/error handling

### 📊 Analytics Dashboard
- Overview with quick stats
- Responsive sidebar navigation
- Mobile-friendly design
- Extensible for future metrics

## 🔧 API Endpoints

### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout
- `GET /api/auth/check` - Check auth status

### Maps & Dealers
- `GET /api/map/dealers` - Get dealer locations
- `POST /api/map/generate` - Generate map HTML

### Images
- `GET /api/images/gallery` - Get image gallery

### Forms
- `POST /api/form/submit` - Submit Mimo form
- `GET /api/form/tickets` - Get form tickets

### Data
- `GET /api/data/csv` - Get CSV data
- `GET /api/health` - Health check

## 🛠️ Development

### Environment Variables
Copy `.env.example` to `.env` and configure:
```bash
cp .env.example .env
```

### Available Scripts
```bash
# Development
npm run dev              # Start React dev server
npm run start-backend    # Start Flask backend
npm run start           # Start both (requires concurrently)

# Production
npm run build           # Build React for production
npm run preview         # Preview production build

# Utilities
npm run lint            # Run ESLint
npm run setup           # Install all dependencies
```

### Adding New Components
1. Create component in `src/components/`
2. Add to `Dashboard.tsx` navigation
3. Create corresponding API endpoint in `flask_backend.py`
4. Update API service in `src/services/api.ts`

## 🔒 Security Features

- CSRF protection via session tokens
- Input validation and sanitization
- Secure file upload handling
- CORS configuration
- Environment-based secrets

## 📱 Responsive Design

- Mobile-first approach
- Collapsible sidebar navigation
- Touch-friendly interactions
- Responsive grid layouts
- Optimized for tablets and phones

## 🚀 Deployment

### Development
```bash
python start_integrated.py
```

### Production
```bash
# Build frontend
npm run build

# Start with production server
gunicorn -w 4 -b 0.0.0.0:5000 flask_backend:app

# Serve built frontend with nginx or similar
```

## 🐛 Troubleshooting

### Common Issues

1. **Port already in use**
   ```bash
   # Kill processes on ports 3000 and 5000
   npx kill-port 3000 5000
   ```

2. **Python dependencies missing**
   ```bash
   pip install -r flask_requirements.txt
   ```

3. **Node.js dependencies missing**
   ```bash
   npm install
   ```

4. **CORS errors**
   - Check `CORS_ORIGINS` in `.env`
   - Ensure frontend URL matches CORS config

5. **Data files missing**
   - Check if CSV files exist in root directory
   - Some features may be limited without data

### Debug Mode
```bash
# Enable debug logging
FLASK_DEBUG=1 python flask_backend.py

# React development mode (automatic)
npm run dev
```

## 📈 Performance

- **Frontend**: Vite for fast development and optimized builds
- **Backend**: Flask with efficient pandas operations
- **Images**: Base64 encoding with lazy loading
- **Maps**: Leaflet with marker clustering for large datasets

## 🤝 Contributing

1. Fork the repository
2. Create feature branch: `git checkout -b feature-name`
3. Commit changes: `git commit -am 'Add feature'`
4. Push to branch: `git push origin feature-name`
5. Submit pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Check the troubleshooting section above
- Review API documentation in `README_FLASK_BACKEND.md`
- Create an issue in the repository

---

**Built with ❤️ using Flask, React, TypeScript, and Tailwind CSS**
