import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>ef<PERSON>, Refresh<PERSON><PERSON>, Eye, EyeOff, Download, ZoomIn, ZoomOut } from 'lucide-react';

interface InteractiveMapProps {
  selectedBrand: string;
  selectedState: string;
  selectedGeography: string;
  selectedLocality: string;
  onBack: () => void;
}

interface GalleryImage {
  name: string;
  base64: string;
  brand: string;
  state: string;
  coordinates: string;
  timestamp: string;
  path: string;
}

export const InteractiveMap: React.FC<InteractiveMapProps> = ({
  selectedBrand,
  selectedState,
  selectedGeography,
  selectedLocality,
  onBack
}) => {
  const [mapHtml, setMapHtml] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [images, setImages] = useState<GalleryImage[]>([]);
  const [showCompetitors, setShowCompetitors] = useState<boolean>(false);
  const [selectedImage, setSelectedImage] = useState<GalleryImage | null>(null);
  const [currentImageIndex, setCurrentImageIndex] = useState<number>(0);

  useEffect(() => {
    loadMap();
    loadImages();
  }, [selectedState, selectedBrand]);

  useEffect(() => {
    loadImages();
  }, [showCompetitors]);

  const loadMap = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch(`/api/interactive-map/generate?state=${encodeURIComponent(selectedState)}`);
      
      if (!response.ok) {
        throw new Error(`Failed to load map: ${response.statusText}`);
      }
      
      const data = await response.json();
      
      if (data.success && data.html) {
        setMapHtml(data.html);
      } else {
        throw new Error(data.error || 'Failed to load map');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load interactive map');
    } finally {
      setLoading(false);
    }
  };

  const loadImages = async () => {
    try {
      const params = new URLSearchParams({
        state: selectedState,
        show_competitors: showCompetitors.toString()
      });
      
      if (selectedBrand && selectedBrand !== "All Brands") {
        params.append('brand', selectedBrand);
      }
      
      const response = await fetch(`/api/images/gallery?${params}`);
      if (!response.ok) {
        throw new Error('Failed to load images');
      }
      
      const data = await response.json();
      setImages(data.images || []);
    } catch (err) {
      console.error('Error loading images:', err);
      setImages([]);
    }
  };

  const openModal = (image: GalleryImage, index: number) => {
    setSelectedImage(image);
    setCurrentImageIndex(index);
  };

  const closeModal = () => {
    setSelectedImage(null);
  };

  const navigateImage = (direction: 'prev' | 'next') => {
    const newIndex = direction === 'prev' 
      ? (currentImageIndex - 1 + images.length) % images.length
      : (currentImageIndex + 1) % images.length;
    
    setCurrentImageIndex(newIndex);
    setSelectedImage(images[newIndex]);
  };

  const downloadImage = (image: GalleryImage) => {
    const link = document.createElement('a');
    link.href = `data:image/jpeg;base64,${image.base64}`;
    link.download = image.name;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <div className="h-full bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <button
              onClick={onBack}
              className="flex items-center px-3 py-2 text-gray-600 hover:text-gray-800 transition-colors"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Dashboard
            </button>
            <div className="h-6 w-px bg-gray-300"></div>
            <h1 className="text-xl font-bold text-gray-800">
              🗺️ {selectedState === "Delhi(NCT)" ? "Delhi" : "Interactive"} Geographic Intelligence
            </h1>
          </div>
          <button
            onClick={loadMap}
            className="flex items-center px-3 py-2 bg-orange-500 text-white rounded hover:bg-orange-600 transition-colors"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Regenerate Map
          </button>
        </div>
        
        {/* Location Info */}
        <div className="mt-4 p-4 bg-gradient-to-r from-orange-50 to-white border-2 border-orange-200 rounded-lg">
          <h4 className="text-orange-600 font-semibold mb-2">📍 Selected Location</h4>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span className="font-medium text-gray-700">State:</span>
              <span className="ml-2 text-gray-600">{selectedState}</span>
            </div>
            <div>
              <span className="font-medium text-gray-700">Geography:</span>
              <span className="ml-2 text-gray-600">{selectedGeography}</span>
            </div>
            <div>
              <span className="font-medium text-gray-700">Locality:</span>
              <span className="ml-2 text-gray-600">{selectedLocality}</span>
            </div>
            <div>
              <span className="font-medium text-gray-700">Brand:</span>
              <span className="ml-2 text-orange-600 font-medium">
                {selectedBrand === "All Brands" ? "All Brands" : selectedBrand}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Map Section */}
      <div className="p-6">
        <div className="bg-white rounded-lg shadow-sm border">
          <div className="p-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-800">Interactive Map</h2>
          </div>
          <div className="p-4">
            {loading ? (
              <div className="flex items-center justify-center h-96 bg-gray-50 rounded-lg">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500 mx-auto mb-4"></div>
                  <p className="text-gray-600">Generating live map with real geocoding...</p>
                </div>
              </div>
            ) : error ? (
              <div className="flex items-center justify-center h-96 bg-red-50 rounded-lg border border-red-200">
                <div className="text-center">
                  <div className="text-red-500 text-4xl mb-4">⚠️</div>
                  <h3 className="text-lg font-semibold text-red-800 mb-2">Map Not Available</h3>
                  <p className="text-red-600 mb-4">{error}</p>
                  <button
                    onClick={loadMap}
                    className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
                  >
                    Try Again
                  </button>
                </div>
              </div>
            ) : (
              <div 
                className="w-full h-96 border border-gray-200 rounded-lg overflow-hidden"
                dangerouslySetInnerHTML={{ __html: mapHtml }}
              />
            )}
          </div>
        </div>
      </div>

      {/* Image Gallery Section */}
      <div className="p-6 pt-0">
        <div className="bg-white rounded-lg shadow-sm border">
          <div className="p-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-semibold text-gray-800">📸 Interactive Image Gallery</h2>
              <div className="flex items-center space-x-4">
                <button
                  onClick={() => setShowCompetitors(!showCompetitors)}
                  className={`flex items-center px-3 py-2 rounded transition-colors ${
                    showCompetitors 
                      ? 'bg-orange-100 text-orange-700 border border-orange-300' 
                      : 'bg-gray-100 text-gray-700 border border-gray-300'
                  }`}
                >
                  {showCompetitors ? <Eye className="w-4 h-4 mr-2" /> : <EyeOff className="w-4 h-4 mr-2" />}
                  {showCompetitors ? 'Hide Competitors' : 'Show Competitors'}
                </button>
                <span className="text-sm text-gray-600">
                  {images.length} images found
                </span>
              </div>
            </div>
            
            {/* Gallery Info */}
            <div className="mt-2 text-sm text-gray-600">
              {selectedBrand && selectedBrand !== "All Brands" && !showCompetitors && (
                <span>Showing images for brand: <strong>{selectedBrand}</strong></span>
              )}
              {showCompetitors && (
                <span>Showing all competitor images</span>
              )}
              {(!selectedBrand || selectedBrand === "All Brands") && !showCompetitors && (
                <span>Showing all brands</span>
              )}
            </div>
          </div>
          
          <div className="p-4">
            {images.length > 0 ? (
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                {images.map((image, index) => (
                  <div
                    key={`${image.path}-${index}`}
                    className="bg-white rounded-lg shadow-sm border overflow-hidden hover:shadow-md transition-shadow cursor-pointer"
                    onClick={() => openModal(image, index)}
                  >
                    <div className="aspect-square relative overflow-hidden">
                      <img
                        src={`data:image/jpeg;base64,${image.base64}`}
                        alt={`${image.brand || 'General'} Image`}
                        className="w-full h-full object-cover hover:scale-105 transition-transform duration-200"
                        loading="lazy"
                      />
                    </div>
                    <div className="p-3">
                      <div className="font-medium text-gray-800 text-sm mb-1">
                        {image.brand || 'General'}
                      </div>
                      <div className="text-xs text-gray-600 space-y-1">
                        <div>🕒 {image.timestamp}</div>
                        <div>📍 {image.coordinates}</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12 text-gray-500">
                <div className="text-4xl mb-4">📁</div>
                <h3 className="text-lg font-medium mb-2">No images found</h3>
                <p className="text-sm">
                  {showCompetitors 
                    ? "No competitor images available for this state"
                    : selectedBrand && selectedBrand !== "All Brands"
                      ? `No images found for brand "${selectedBrand}"`
                      : "No images available in the gallery"
                  }
                </p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Image Modal */}
      {selectedImage && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-4xl max-h-full overflow-hidden">
            <div className="p-4 border-b border-gray-200 flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-800">
                {selectedImage.brand || 'General'} Image
              </h3>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => downloadImage(selectedImage)}
                  className="p-2 text-gray-600 hover:text-gray-800 transition-colors"
                >
                  <Download className="w-4 h-4" />
                </button>
                <button
                  onClick={closeModal}
                  className="p-2 text-gray-600 hover:text-gray-800 transition-colors"
                >
                  ✕
                </button>
              </div>
            </div>
            <div className="p-4">
              <img
                src={`data:image/jpeg;base64,${selectedImage.base64}`}
                alt={selectedImage.name}
                className="max-w-full max-h-96 mx-auto"
              />
              <div className="mt-4 text-sm text-gray-600 space-y-1">
                <div>🕒 {selectedImage.timestamp}</div>
                <div>📍 {selectedImage.coordinates}</div>
              </div>
            </div>
            <div className="p-4 border-t border-gray-200 flex items-center justify-between">
              <button
                onClick={() => navigateImage('prev')}
                disabled={images.length <= 1}
                className="px-4 py-2 bg-gray-100 text-gray-700 rounded hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                Previous
              </button>
              <span className="text-sm text-gray-600">
                {currentImageIndex + 1} of {images.length}
              </span>
              <button
                onClick={() => navigateImage('next')}
                disabled={images.length <= 1}
                className="px-4 py-2 bg-gray-100 text-gray-700 rounded hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                Next
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
