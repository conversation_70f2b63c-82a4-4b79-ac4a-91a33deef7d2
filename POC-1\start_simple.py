#!/usr/bin/env python3
"""
Simple startup script for POC-1 Dashboard
Alternative to start_integrated.py with better Windows npm handling
"""

import os
import sys
import subprocess
import time

def check_python_deps():
    """Check Python dependencies"""
    try:
        import flask, pandas, folium, geopy
        print("✅ Python dependencies are available")
        return True
    except ImportError as e:
        print(f"❌ Missing Python dependency: {e}")
        print("📦 Installing Python dependencies...")
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", "-r", "flask_requirements.txt"], check=True)
            print("✅ Python dependencies installed")
            return True
        except:
            print("❌ Failed to install Python dependencies")
            return False

def find_npm():
    """Find npm command that works"""
    npm_commands = ['npm', 'npm.cmd', 'npm.exe']
    
    for cmd in npm_commands:
        try:
            result = subprocess.run([cmd, '--version'], capture_output=True, text=True, shell=True)
            if result.returncode == 0:
                print(f"✅ Found npm: {cmd} (version {result.stdout.strip()})")
                return cmd
        except:
            continue
    
    print("❌ npm not found. Trying alternative approaches...")
    
    # Try with full path
    possible_paths = [
        r"C:\Program Files\nodejs\npm.cmd",
        r"C:\Program Files (x86)\nodejs\npm.cmd",
        os.path.expanduser("~\\AppData\\Roaming\\npm\\npm.cmd")
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            try:
                result = subprocess.run([path, '--version'], capture_output=True, text=True)
                if result.returncode == 0:
                    print(f"✅ Found npm at: {path}")
                    return path
            except:
                continue
    
    return None

def install_node_deps(npm_cmd):
    """Install Node.js dependencies"""
    if not os.path.exists("node_modules"):
        print("📦 Installing Node.js dependencies...")
        try:
            subprocess.run([npm_cmd, 'install'], check=True, shell=True)
            print("✅ Node.js dependencies installed")
            return True
        except subprocess.CalledProcessError:
            print("❌ Failed to install Node.js dependencies")
            return False
    else:
        print("✅ Node.js dependencies already installed")
        return True

def start_backend():
    """Start Flask backend"""
    print("🚀 Starting Flask backend on http://localhost:5000")
    try:
        # Create necessary directories
        os.makedirs("submitted_forms", exist_ok=True)
        
        # Start Flask
        backend_process = subprocess.Popen([
            sys.executable, "flask_backend.py"
        ])
        
        # Wait a moment for Flask to start
        time.sleep(3)
        
        # Check if Flask is running
        try:
            import requests
            response = requests.get('http://localhost:5000/api/health', timeout=5)
            if response.status_code == 200:
                print("✅ Flask backend is running")
            else:
                print("⚠️  Flask backend may not be ready yet")
        except:
            print("⚠️  Flask backend starting...")
        
        return backend_process
    except Exception as e:
        print(f"❌ Failed to start Flask backend: {e}")
        return None

def start_frontend(npm_cmd):
    """Start React frontend"""
    print("🚀 Starting React frontend on http://localhost:3000")
    try:
        frontend_process = subprocess.Popen([
            npm_cmd, 'run', 'dev'
        ], shell=True)
        
        # Wait a moment for React to start
        time.sleep(5)
        
        print("✅ React frontend is starting...")
        return frontend_process
    except Exception as e:
        print(f"❌ Failed to start React frontend: {e}")
        return None

def main():
    """Main function"""
    print("🔧 POC-1 Simple Dashboard Startup")
    print("=" * 40)
    
    # Check if we're in the right directory
    if not os.path.exists("flask_backend.py") or not os.path.exists("package.json"):
        print("❌ Required files not found!")
        print("   Please run this script from the POC-1 directory")
        sys.exit(1)
    
    # Check Python dependencies
    if not check_python_deps():
        print("❌ Cannot proceed without Python dependencies")
        sys.exit(1)
    
    # Find npm
    npm_cmd = find_npm()
    if not npm_cmd:
        print("❌ npm not found!")
        print("\n💡 Solutions:")
        print("1. Restart your command prompt/terminal")
        print("2. Reinstall Node.js from https://nodejs.org/")
        print("3. Add Node.js to your PATH environment variable")
        print("4. Run only the backend: python flask_backend.py")
        
        # Offer to run backend only
        choice = input("\nWould you like to run only the Flask backend? (y/n): ")
        if choice.lower() == 'y':
            print("\n🚀 Starting Flask backend only...")
            backend_process = start_backend()
            if backend_process:
                print("\n" + "=" * 50)
                print("🎉 Flask Backend is running!")
                print("📍 Backend API: http://localhost:5000")
                print("📋 Health Check: http://localhost:5000/api/health")
                print("\nPress Ctrl+C to stop")
                print("=" * 50)
                try:
                    backend_process.wait()
                except KeyboardInterrupt:
                    print("\n🛑 Stopping backend...")
                    backend_process.terminate()
        sys.exit(1)
    
    # Install Node.js dependencies
    if not install_node_deps(npm_cmd):
        print("❌ Cannot proceed without Node.js dependencies")
        sys.exit(1)
    
    print("\n🚀 Starting both servers...")
    print("=" * 40)
    
    # Start backend
    backend_process = start_backend()
    if not backend_process:
        print("❌ Failed to start backend")
        sys.exit(1)
    
    # Start frontend
    frontend_process = start_frontend(npm_cmd)
    if not frontend_process:
        print("❌ Failed to start frontend")
        backend_process.terminate()
        sys.exit(1)
    
    print("\n" + "=" * 50)
    print("🎉 POC-1 Dashboard is running!")
    print("=" * 50)
    print("📍 Frontend: http://localhost:3000")
    print("📍 Backend: http://localhost:5000")
    print("\n🔐 Login Credentials:")
    print("   Email: <EMAIL>")
    print("   Password: user")
    print("\n" + "=" * 50)
    print("Press Ctrl+C to stop both servers")
    print("=" * 50)
    
    try:
        # Wait for user to stop
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n🛑 Stopping servers...")
        if frontend_process:
            frontend_process.terminate()
        if backend_process:
            backend_process.terminate()
        print("✅ Servers stopped")

if __name__ == "__main__":
    main()
