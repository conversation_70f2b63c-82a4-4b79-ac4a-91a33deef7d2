import React from 'react';
import { useAuth } from './AuthProvider';
import { LoginForm } from './LoginForm';
import { Loader } from 'lucide-react';

interface ProtectedRouteProps {
  children: React.ReactNode;
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const { isAuthenticated, isLoading, login } = useAuth();
  const [showSkipOption, setShowSkipOption] = React.useState(false);

  console.log('ProtectedRoute render:', { isAuthenticated, isLoading });

  // Show skip option after 3 seconds of loading
  React.useEffect(() => {
    if (isLoading) {
      const timer = setTimeout(() => {
        setShowSkipOption(true);
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [isLoading]);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <Loader className="w-8 h-8 animate-spin mx-auto mb-4 text-orange-500" />
          <p className="text-gray-600">Checking authentication...</p>
          <p className="text-sm text-gray-500 mt-2">
            If this takes too long, the login form will appear automatically
          </p>
          {showSkipOption && (
            <button
              onClick={() => window.location.reload()}
              className="mt-4 px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600 transition-colors"
            >
              Refresh Page
            </button>
          )}
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return <LoginForm onLogin={login} isLoading={isLoading} />;
  }

  return <>{children}</>;
};
