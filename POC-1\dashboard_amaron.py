
import streamlit as st
import pandas as pd
import plotly.express as px




# Load the dataset - try multiple possible locations
import os

possible_files = [
    "Merged_Amaron_Population_SouthDelhi.xlsx",
    "YusufSaraiAmaronCentre-FinalCSV.xlsx",
    "Centres/Delhi/YusufSaraiAmaronCentre-FinalCSV.xlsx"
]

df = None
for file_path in possible_files:
    if os.path.exists(file_path):
        try:
            df = pd.read_excel(file_path)
            st.info(f"✅ Loaded data from: {file_path}")
            break
        except Exception as e:
            st.warning(f"⚠️ Could not read {file_path}: {e}")
            continue

if df is None:
    st.error("❌ No valid data file found. Please ensure one of these files exists:")
    for file_path in possible_files:
        st.write(f"- {file_path}")
    st.stop()

st.set_page_config(page_title="Amaron RetailIQ Dashboard", layout="wide")
st.title("📊 Amaron Retail Intelligence – South Delhi")
st.dataframe(df.head())
st.title("💡 DEBUG: Dashboard Loaded")
st.write("Data Preview:")

# --- Sidebar Filters ---
st.sidebar.header("🔍 Filter Data")
sub_districts = st.sidebar.multiselect("Select Sub-District", sorted(df['Sub-District'].unique()))
pincodes = st.sidebar.multiselect("Select Pincode", sorted(df['Pincode'].unique()))

filtered_df = df.copy()
if sub_districts:
    filtered_df = filtered_df[filtered_df['Sub-District'].isin(sub_districts)]
if pincodes:
    filtered_df = filtered_df[filtered_df['Pincode'].isin(pincodes)]

# --- KPI Metrics ---
st.subheader("📌 Key Performance Indicators")

k1, k2, k3 = st.columns(3)
k1.metric("Total Turnover", f"₹{filtered_df['Total Turnover'].sum():,.0f}")
k2.metric("Avg Turnover per Capita", f"₹{filtered_df['Turnover per Capita'].mean():.2f}")
k3.metric("Avg Dealers per 10k", f"{filtered_df['Dealers per 10k People'].mean():.2f}")

# --- Charts Section ---
st.markdown("### 💰 Turnover by Sub-District")
fig1 = px.bar(filtered_df, x='Sub-District', y='Total Turnover', color='Sub-District', text_auto=True)
st.plotly_chart(fig1, use_container_width=True)

st.markdown("### 👥 Turnover per Capita by Area")
fig2 = px.bar(filtered_df, x='Pincode', y='Turnover per Capita', color='Sub-District', text_auto=".2f")
st.plotly_chart(fig2, use_container_width=True)

st.markdown("### 🛠️ Dealer Saturation (Dealers per 10k People)")
fig3 = px.bar(filtered_df, x='Pincode', y='Dealers per 10k People', color='Sub-District', text_auto=".2f")
st.plotly_chart(fig3, use_container_width=True)

# --- Data Table ---
with st.expander("📋 View Filtered Data Table"):
    st.dataframe(filtered_df)

