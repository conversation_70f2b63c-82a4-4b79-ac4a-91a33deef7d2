#!/usr/bin/env python3
"""
Flask Backend API for POC-1 Project
Provides REST endpoints for authentication, mapping, and form functionalities
"""

from flask import Flask, request, jsonify, session
from flask_cors import CORS
import pandas as pd
import folium
from geopy.geocoders import Nominatim
import os
import base64
import json
from datetime import datetime
import glob
import uuid
from time import sleep

app = Flask(__name__)
app.secret_key = 'your-secret-key-change-this-in-production'
CORS(app, supports_credentials=True)

# Configuration - Exact match with Streamlit login.html
VALID_USERS = [
    {"email": "<EMAIL>", "password": "user1234"},
    {"email": "<EMAIL>", "password": "user1234"},
    {"email": "<EMAIL>", "password": "user1234"}
]

# Utility Functions
def encode_image_to_base64(image_path):
    """Convert image to base64 string"""
    try:
        with open(image_path, "rb") as img_file:
            return base64.b64encode(img_file.read()).decode()
    except Exception as e:
        print(f"Error encoding image {image_path}: {e}")
        return None

def get_lat_lon(pincode, geolocator):
    """Get latitude and longitude for a pincode"""
    try:
        location = geolocator.geocode(f"{pincode}, Delhi, India")
        if location:
            return location.latitude, location.longitude
    except Exception as e:
        print(f"Error geocoding {pincode}: {e}")
    return None, None

# Authentication Endpoints
@app.route('/api/auth/login', methods=['POST'])
def login():
    """Authenticate user and create session"""
    try:
        data = request.get_json()
        email = data.get('email', '').strip()
        password = data.get('password', '').strip()
        
        if not email or not password:
            return jsonify({'error': 'Email and password are required'}), 400
        
        # Validate credentials
        user = next((u for u in VALID_USERS if u['email'] == email and u['password'] == password), None)
        
        if user:
            session['authenticated'] = True
            session['user_email'] = email
            session['login_time'] = datetime.now().isoformat()
            
            return jsonify({
                'success': True,
                'message': 'Login successful',
                'user': {'email': email},
                'session_id': session.get('_id', str(uuid.uuid4()))
            })
        else:
            return jsonify({'error': 'Invalid credentials'}), 401
            
    except Exception as e:
        return jsonify({'error': f'Login failed: {str(e)}'}), 500

@app.route('/api/auth/logout', methods=['POST'])
def logout():
    """Logout user and clear session"""
    session.clear()
    return jsonify({'success': True, 'message': 'Logged out successfully'})

@app.route('/api/auth/check', methods=['GET'])
def check_auth():
    """Check if user is authenticated"""
    if session.get('authenticated'):
        return jsonify({
            'authenticated': True,
            'user': {'email': session.get('user_email')},
            'login_time': session.get('login_time')
        })
    return jsonify({'authenticated': False}), 401

# Map Utilities Endpoints
@app.route('/api/map/dealers', methods=['GET'])
def get_dealers():
    """Get dealer data with coordinates"""
    try:
        # Load dealer data
        dealers = []
        csv_files = [
            ('amaron_retailer_image.csv', 'Amaron', 'red'),
            ('exide_retailer_image.csv', 'Exide', 'blue'),
            ('tata_green_retailer_image.csv', 'Tata Green', 'green'),
            ('luminous_retailer_image.csv', 'Luminous', 'yellow')
        ]
        
        geolocator = Nominatim(user_agent="dealer-mapper-api")
        
        for csv_file, brand, color in csv_files:
            if os.path.exists(csv_file):
                df = pd.read_csv(csv_file)
                for _, row in df.iterrows():
                    lat, lon = get_lat_lon(row.get('PIN', ''), geolocator)
                    if lat and lon:
                        dealers.append({
                            'name': row.get('Name', ''),
                            'pin': row.get('PIN', ''),
                            'brand': brand,
                            'color': color,
                            'lat': lat,
                            'lon': lon,
                            'image': row.get('image', ''),
                            'address': row.get('Address', '')
                        })
                    sleep(0.5)  # Rate limiting for geocoding
        
        return jsonify({'dealers': dealers})
        
    except Exception as e:
        return jsonify({'error': f'Failed to load dealers: {str(e)}'}), 500

@app.route('/api/map/generate', methods=['POST'])
def generate_map():
    """Generate interactive map HTML"""
    try:
        data = request.get_json()
        center_lat = data.get('center_lat', 28.6139)
        center_lon = data.get('center_lon', 77.2090)
        zoom = data.get('zoom', 11)
        
        # Create map
        map_obj = folium.Map(location=[center_lat, center_lon], zoom_start=zoom)
        
        # Get dealers data
        dealers_response = get_dealers()
        if dealers_response[1] == 200:  # Success
            dealers_data = json.loads(dealers_response[0].data)
            dealers = dealers_data.get('dealers', [])
            
            # Add markers for each dealer
            for dealer in dealers:
                popup_html = f"""
                    <b>{dealer['brand']} Dealer:</b> {dealer['name']}<br>
                    <b>PIN:</b> {dealer['pin']}<br>
                    <b>Address:</b> {dealer.get('address', 'N/A')}<br>
                """
                
                if dealer.get('image'):
                    image_path = f"images/{dealer['image']}"
                    if os.path.exists(image_path):
                        popup_html += f"<img src='images/{dealer['image']}' width='200' height='150'>"
                
                folium.Marker(
                    location=[dealer['lat'], dealer['lon']],
                    popup=folium.Popup(popup_html, max_width=250),
                    icon=folium.Icon(color=dealer['color'], icon='info-sign')
                ).add_to(map_obj)
        
        # Add layer control
        folium.LayerControl().add_to(map_obj)
        
        return jsonify({'map_html': map_obj._repr_html_()})
        
    except Exception as e:
        return jsonify({'error': f'Failed to generate map: {str(e)}'}), 500

# Interactive Map Endpoints
@app.route('/api/images/gallery', methods=['GET'])
def get_image_gallery():
    """Get images for gallery display"""
    try:
        state = request.args.get('state', 'Delhi')
        brand_filter = request.args.get('brand', '')
        show_competitors = request.args.get('show_competitors', 'false').lower() == 'true'
        
        images = []
        base_path = f"interactive_images/{state}"
        
        if os.path.exists(base_path):
            for root, dirs, files in os.walk(base_path):
                for file in files:
                    if file.lower().endswith(('.jpg', '.jpeg', '.png', '.gif', '.bmp')):
                        file_path = os.path.join(root, file)
                        folder_name = os.path.basename(root)
                        
                        # Brand filtering
                        if brand_filter and brand_filter.lower() not in folder_name.lower():
                            continue
                            
                        if not show_competitors and 'competitor' in folder_name.lower():
                            continue
                        
                        # Encode image to base64
                        img_base64 = encode_image_to_base64(file_path)
                        if img_base64:
                            images.append({
                                'name': file,
                                'folder': folder_name,
                                'brand': folder_name,
                                'base64': img_base64,
                                'path': file_path,
                                'state': state,
                                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                            })
        
        return jsonify({'images': images, 'total': len(images)})
        
    except Exception as e:
        return jsonify({'error': f'Failed to load images: {str(e)}'}), 500

# Mimo Form Endpoints
@app.route('/api/form/submit', methods=['POST'])
def submit_mimo_form():
    """Submit Mimo form data"""
    try:
        data = request.get_json()
        
        # Generate ticket ID
        ticket_id = f"MIMO-{datetime.now().strftime('%Y%m%d')}-{str(uuid.uuid4())[:8].upper()}"
        
        # Add metadata
        form_data = {
            'ticket_id': ticket_id,
            'submitted_at': datetime.now().isoformat(),
            'user_email': session.get('user_email', 'anonymous'),
            'form_data': data
        }
        
        # Save to file (in production, use database)
        forms_dir = 'submitted_forms'
        os.makedirs(forms_dir, exist_ok=True)
        
        with open(f"{forms_dir}/{ticket_id}.json", 'w') as f:
            json.dump(form_data, f, indent=2)
        
        return jsonify({
            'success': True,
            'ticket_id': ticket_id,
            'message': 'Form submitted successfully'
        })
        
    except Exception as e:
        return jsonify({'error': f'Form submission failed: {str(e)}'}), 500

@app.route('/api/form/tickets', methods=['GET'])
def get_form_tickets():
    """Get list of submitted form tickets"""
    try:
        forms_dir = 'submitted_forms'
        tickets = []
        
        if os.path.exists(forms_dir):
            for filename in os.listdir(forms_dir):
                if filename.endswith('.json'):
                    with open(os.path.join(forms_dir, filename), 'r') as f:
                        ticket_data = json.load(f)
                        tickets.append({
                            'ticket_id': ticket_data['ticket_id'],
                            'submitted_at': ticket_data['submitted_at'],
                            'user_email': ticket_data['user_email']
                        })
        
        return jsonify({'tickets': tickets})
        
    except Exception as e:
        return jsonify({'error': f'Failed to load tickets: {str(e)}'}), 500

# Data Endpoints
@app.route('/api/data/csv', methods=['GET'])
def get_csv_data():
    """Get CSV data for dashboard"""
    try:
        filename = request.args.get('file', 'DataValues-Dashboard-CSV.csv')
        
        if os.path.exists(filename):
            df = pd.read_csv(filename)
            return jsonify({
                'data': df.to_dict('records'),
                'columns': df.columns.tolist(),
                'total_rows': len(df)
            })
        else:
            return jsonify({'error': 'File not found'}), 404
            
    except Exception as e:
        return jsonify({'error': f'Failed to load CSV: {str(e)}'}), 500

# Analytics Endpoints - Matching Streamlit functionality
@app.route('/api/analytics/dashboard', methods=['GET'])
def get_dashboard_data():
    """Get dashboard analytics data"""
    try:
        # Load main dashboard CSV
        csv_file = 'DataValues-Dashboard-CSV.csv'
        if os.path.exists(csv_file):
            df = pd.read_csv(csv_file)

            # Calculate analytics similar to Streamlit app
            analytics = {
                'total_records': len(df),
                'unique_states': df['State'].nunique() if 'State' in df.columns else 0,
                'unique_geographies': df['Geography'].nunique() if 'Geography' in df.columns else 0,
                'data_summary': df.describe().to_dict() if not df.empty else {},
                'column_info': {
                    'columns': df.columns.tolist(),
                    'dtypes': df.dtypes.astype(str).to_dict()
                }
            }

            return jsonify(analytics)
        else:
            return jsonify({'error': 'Dashboard data file not found'}), 404

    except Exception as e:
        return jsonify({'error': f'Failed to load dashboard data: {str(e)}'}), 500

@app.route('/api/analytics/geographic', methods=['GET'])
def get_geographic_data():
    """Get geographic analytics data"""
    try:
        state = request.args.get('state', 'Delhi(NCT)')
        geography = request.args.get('geography', 'South Central')
        locality = request.args.get('locality', 'Yusuf Sarai')

        # Validate geographic combination
        valid_combinations = [
            ["Delhi(NCT)", "South Central", "Yusuf Sarai"],
            ["West Bengal", "East", "South 24 Parganas"]
        ]

        current_combination = [state, geography, locality]
        is_valid = any(combo == current_combination for combo in valid_combinations)

        if not is_valid:
            return jsonify({
                'valid': False,
                'message': 'Libraries are not connected',
                'valid_combinations': valid_combinations
            })

        # Load relevant data for the geographic combination
        data = {
            'valid': True,
            'state': state,
            'geography': geography,
            'locality': locality,
            'analytics': {
                'population': 125000 if state == "Delhi(NCT)" else 89000,
                'market_size': 'Large' if state == "Delhi(NCT)" else 'Medium',
                'growth_rate': 12.5 if state == "Delhi(NCT)" else 8.3
            }
        }

        return jsonify(data)

    except Exception as e:
        return jsonify({'error': f'Failed to load geographic data: {str(e)}'}), 500

@app.route('/api/analytics/excel', methods=['GET'])
def get_excel_data():
    """Get Excel file data - matching Streamlit's Excel processing"""
    try:
        excel_file = 'YusufSaraiAmaronCentre-FinalCSV.xlsx'
        if os.path.exists(excel_file):
            # Read Excel file similar to Streamlit app
            df = pd.read_excel(excel_file)

            return jsonify({
                'data': df.to_dict('records'),
                'columns': df.columns.tolist(),
                'total_rows': len(df),
                'file_info': {
                    'filename': excel_file,
                    'sheet_names': pd.ExcelFile(excel_file).sheet_names
                }
            })
        else:
            return jsonify({'error': 'Excel file not found'}), 404

    except Exception as e:
        return jsonify({'error': f'Failed to load Excel data: {str(e)}'}), 500

# GeoIQ Images Endpoint - Matching Streamlit's image processing
@app.route('/api/geoiq/images', methods=['GET'])
def get_geoiq_images():
    """Get GeoIQ analysis images"""
    try:
        images_dir = 'GeoIQimages'
        images = []

        if os.path.exists(images_dir):
            for filename in os.listdir(images_dir):
                if filename.lower().endswith(('.png', '.jpg', '.jpeg', '.gif')):
                    image_path = os.path.join(images_dir, filename)
                    img_base64 = encode_image_to_base64(image_path)
                    if img_base64:
                        images.append({
                            'name': filename,
                            'base64': img_base64,
                            'path': image_path
                        })

        return jsonify({'images': images, 'total': len(images)})

    except Exception as e:
        return jsonify({'error': f'Failed to load GeoIQ images: {str(e)}'}), 500

# Category Images Endpoint - Matching Streamlit's item image functionality
@app.route('/api/images/<filename>', methods=['GET'])
def get_category_image(filename):
    """Get category/subcategory images"""
    try:
        # Try different image directories based on state
        image_dirs = [
            'Items/YusufSaraiItemPrice',
            'Items/South24ParganasItemPrices',
            'GeoIQimages/YusufSaraiGEOIQImages',
            'GeoIQimages/South24ParganasGEOIQImages',
            'interactive_images'
        ]

        for image_dir in image_dirs:
            if os.path.exists(image_dir):
                image_path = os.path.join(image_dir, filename)
                if os.path.exists(image_path):
                    img_base64 = encode_image_to_base64(image_path)
                    if img_base64:
                        return jsonify({
                            'success': True,
                            'base64': img_base64,
                            'filename': filename,
                            'path': image_path
                        })

        return jsonify({'error': f'Image {filename} not found'}), 404

    except Exception as e:
        return jsonify({'error': f'Failed to load image: {str(e)}'}), 500

# Interactive Images Endpoint - Matching Streamlit's interactive gallery
@app.route('/api/interactive/images', methods=['GET'])
def get_interactive_images():
    """Get interactive gallery images with brand filtering"""
    try:
        search_query = request.args.get('search', '')
        state = request.args.get('state', 'Delhi(NCT)')
        show_competitors = request.args.get('show_competitors', 'false').lower() == 'true'

        images_dir = 'interactive_images'
        images = []

        if os.path.exists(images_dir):
            # Walk through all subdirectories
            for root, dirs, files in os.walk(images_dir):
                for filename in files:
                    if filename.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.bmp')):
                        image_path = os.path.join(root, filename)

                        # Extract brand from folder structure or filename
                        brand = extract_brand_from_path(image_path)

                        # Filter by search query if provided
                        if search_query and not show_competitors:
                            if search_query.lower() not in brand.lower() and search_query.lower() not in filename.lower():
                                continue

                        img_base64 = encode_image_to_base64(image_path)
                        if img_base64:
                            images.append({
                                'name': filename,
                                'brand': brand,
                                'base64': img_base64,
                                'path': image_path,
                                'state': state,
                                'folder': os.path.basename(root),
                                'timestamp': 'Recent',
                                'coordinates': 'Unknown',
                                'location': 'Unknown'
                            })

        return jsonify({'images': images, 'total': len(images)})

    except Exception as e:
        return jsonify({'error': f'Failed to load interactive images: {str(e)}'}), 500

# Analytics Images Endpoint - Matching Streamlit's analytics image functionality
@app.route('/api/analytics/image/<image_name>', methods=['GET'])
def get_analytics_image(image_name):
    """Get analytics images based on state and image name"""
    try:
        state = request.args.get('state', 'Delhi(NCT)')

        # Map state to image folder (matching Streamlit's get_image_folder_by_state function)
        state_folder_mapping = {
            'Delhi(NCT)': 'YusufSaraiGEOIQImages',
            'West Bengal': 'South24ParganasGEOIQImages'
        }

        folder_name = state_folder_mapping.get(state, 'YusufSaraiGEOIQImages')
        image_dir = f'GeoIQimages/{folder_name}'

        # Try to find the image file
        image_filename = f"{image_name}.png"
        image_path = os.path.join(image_dir, image_filename)

        if os.path.exists(image_path):
            img_base64 = encode_image_to_base64(image_path)
            if img_base64:
                return jsonify({
                    'success': True,
                    'base64': img_base64,
                    'filename': image_filename,
                    'path': image_path,
                    'state': state,
                    'folder': folder_name
                })

        # If not found, try alternative locations
        alternative_dirs = [
            'GeoIQimages',
            f'GeoIQimages/{state}',
            'interactive_images',
            f'interactive_images/{state}'
        ]

        for alt_dir in alternative_dirs:
            if os.path.exists(alt_dir):
                alt_path = os.path.join(alt_dir, image_filename)
                if os.path.exists(alt_path):
                    img_base64 = encode_image_to_base64(alt_path)
                    if img_base64:
                        return jsonify({
                            'success': True,
                            'base64': img_base64,
                            'filename': image_filename,
                            'path': alt_path,
                            'state': state,
                            'folder': os.path.basename(alt_dir)
                        })

        return jsonify({
            'success': False,
            'error': f'Analytics image "{image_filename}" not found for state "{state}"'
        }), 404

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'Failed to load analytics image: {str(e)}'
        }), 500

# Category Images Endpoint - Matching Streamlit's sub-category image functionality
@app.route('/api/category/image/<image_name>', methods=['GET'])
def get_category_image_by_name(image_name):
    """Get category/subcategory images based on state and image name"""
    try:
        print(f"Category image request: {image_name}")
        state = request.args.get('state', 'Delhi(NCT)')
        print(f"State: {state}")

        # Map state to item image folder (matching Streamlit's get_item_image_folder_by_state function)
        state_folder_mapping = {
            'Delhi(NCT)': 'YusufSaraiItemPrice',
            'West Bengal': 'South24ParganasItemPrices'
        }

        folder_name = state_folder_mapping.get(state, 'YusufSaraiItemPrice')
        image_dir = f'Items/{folder_name}'
        print(f"Looking in directory: {image_dir}")

        # Try to find the image file
        image_filename = f"{image_name}.png"
        image_path = os.path.join(image_dir, image_filename)
        print(f"Full image path: {image_path}")

        if os.path.exists(image_path):
            print(f"Image found at: {image_path}")
            img_base64 = encode_image_to_base64(image_path)
            if img_base64:
                print("Image encoded successfully")
                return jsonify({
                    'success': True,
                    'base64': img_base64,
                    'filename': image_filename,
                    'path': image_path,
                    'state': state,
                    'folder': folder_name
                })
            else:
                print("Failed to encode image")
        else:
            print(f"Image not found at: {image_path}")

        return jsonify({
            'success': False,
            'error': f'Category image "{image_filename}" not found for state "{state}"'
        }), 404

    except Exception as e:
        print(f"Error in category image endpoint: {e}")
        return jsonify({
            'success': False,
            'error': f'Failed to load category image: {str(e)}'
        }), 500

def extract_brand_from_path(image_path):
    """Extract brand name from image path or filename"""
    path_parts = image_path.split(os.sep)
    filename = os.path.basename(image_path)

    # Common brand names to look for
    brands = ['amaron', 'exide', 'tata', 'luminous', 'microtek', 'su-kam']

    # Check folder names
    for part in path_parts:
        for brand in brands:
            if brand.lower() in part.lower():
                return brand.capitalize()

    # Check filename
    for brand in brands:
        if brand.lower() in filename.lower():
            return brand.capitalize()

    return 'Unknown Brand'

# Health Check
@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'version': '1.0.0'
    })

@app.route('/api/test/category/<image_name>', methods=['GET'])
def test_category_image(image_name):
    """Test category image endpoint"""
    return jsonify({
        'success': True,
        'message': f'Test endpoint working for {image_name}',
        'timestamp': datetime.now().isoformat()
    })

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
