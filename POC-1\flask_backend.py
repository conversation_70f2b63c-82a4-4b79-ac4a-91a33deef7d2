#!/usr/bin/env python3
"""
Flask Backend API for POC-1 Project
Provides REST endpoints for authentication, mapping, and form functionalities
"""

from flask import Flask, request, jsonify, session
from flask_cors import CORS
import pandas as pd
import folium
from geopy.geocoders import Nominatim
import os
import base64
import json
from datetime import datetime
import glob
import uuid
from time import sleep

app = Flask(__name__)
app.secret_key = 'your-secret-key-change-this-in-production'
CORS(app, supports_credentials=True)

# Configuration - Exact match with Streamlit login.html
VALID_USERS = [
    {"email": "<EMAIL>", "password": "user1234"},
    {"email": "<EMAIL>", "password": "user1234"},
    {"email": "<EMAIL>", "password": "user1234"}
]

# Utility Functions
def encode_image_to_base64(image_path):
    """Convert image to base64 string"""
    try:
        with open(image_path, "rb") as img_file:
            return base64.b64encode(img_file.read()).decode()
    except Exception as e:
        print(f"Error encoding image {image_path}: {e}")
        return None

def generate_delhi_map():
    """Generate interactive Folium map for Delhi with dealer locations"""
    try:
        import folium
        import pandas as pd

        # Load Delhi dealer data from correct path
        csv_file = 'Centres/Delhi/YusufSaraiAmaronCentre-FinalCSV.csv'

        df = None
        if os.path.exists(csv_file):
            try:
                # Try different encodings for the CSV file
                for encoding in ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1']:
                    try:
                        df = pd.read_csv(csv_file, encoding=encoding)
                        print(f"Loaded Delhi CSV with {len(df)} rows using {encoding} encoding")
                        break
                    except UnicodeDecodeError:
                        continue
                else:
                    raise Exception("Could not decode CSV with any encoding")
            except Exception as e:
                print(f"Error loading {csv_file}: {e}")
                df = None

        # Delhi center coordinates (Yusuf Sarai)
        delhi_center = [28.5562, 77.2095]

        # Create Delhi map
        live_map = folium.Map(
            location=delhi_center,
            zoom_start=12,
            tiles='OpenStreetMap'
        )

        # Add Yusuf Sarai market reference marker
        folium.Marker(
            location=delhi_center,
            popup=folium.Popup(
                """
                <div style='width: 250px'>
                    <h4 style='color: #ff6600; margin: 0;'>📍 Yusuf Sarai Market</h4>
                    <p><b>Location:</b> South Delhi<br>
                    <b>Geography:</b> South Central<br>
                    <b>Type:</b> Commercial Hub<br>
                    <b>Specialties:</b> Electronics, Home Appliances</p>
                </div>
                """,
                max_width=300
            ),
            icon=folium.Icon(color='orange', icon='star', prefix='fa')
        ).add_to(live_map)

        # Add dealer markers if data is available
        dealers_added = 0
        if df is not None and len(df) > 0:
            # Delhi area coordinates for different localities (approximate)
            delhi_locations = {
                'LAJPAT NAGAR': [28.5653, 77.2430],
                'SAVITRI NAGAR': [28.5244, 77.2066],
                'KAILASH COLONY': [28.5355, 77.2430],
                'DEFENCE COLONY': [28.5729, 77.2295],
                'GREATER KAILASH': [28.5494, 77.2461],
                'SOUTH EXTENSION': [28.5706, 77.2144],
                'NEHRU PLACE': [28.5494, 77.2519],
                'MALVIYA NAGAR': [28.5244, 77.2066],
                'HAUZ KHAS': [28.5494, 77.2066],
                'GREEN PARK': [28.5594, 77.2066]
            }

            for idx, row in df.iterrows():
                try:
                    dealer_name = row.get('Name', f'Dealer {idx}')
                    address = row.get('Address', 'Address not available')
                    brand = row.get('Brand', 'Unknown')
                    pin = row.get('PIN', 'N/A')

                    # Try to match address to known locations
                    lat, lon = delhi_center  # Default to center
                    for location, coords in delhi_locations.items():
                        if location in address.upper():
                            lat, lon = coords
                            break

                    # Add some random offset to avoid overlapping markers
                    import random
                    lat += random.uniform(-0.01, 0.01)
                    lon += random.uniform(-0.01, 0.01)

                    dealer_info = f"""
                    <div style='width: 300px'>
                        <h4 style='color: #ff6600; margin: 0;'>🏪 {dealer_name}</h4>
                        <p><b>Brand:</b> {brand}<br>
                        <b>Address:</b> {address}<br>
                        <b>PIN:</b> {pin}</p>
                    </div>
                    """

                    folium.Marker(
                        location=(lat, lon),
                        popup=folium.Popup(dealer_info, max_width=320),
                        icon=folium.Icon(color='red', icon='flash', prefix='fa')
                    ).add_to(live_map)
                    dealers_added += 1

                except Exception as e:
                    print(f"Error adding Delhi dealer marker for row {idx}: {e}")
                    continue

        # Add trade area circle
        folium.Circle(
            location=delhi_center,
            radius=5000,  # 5km radius
            popup=f'Delhi Trade Area Coverage<br>({dealers_added} dealers)',
            color='orange',
            fill=True,
            fillOpacity=0.1
        ).add_to(live_map)

        return live_map._repr_html_()

    except Exception as e:
        print(f"Error generating Delhi map: {e}")
        return None

def generate_west_bengal_map():
    """Generate interactive Folium map for West Bengal with dealer locations"""
    try:
        import folium
        import pandas as pd

        # Load West Bengal dealer data from correct path
        csv_file = 'Centres/West bengal/South24ParganasAmaronCenter-CSV.csv'

        df = None
        if os.path.exists(csv_file):
            try:
                # Try different encodings for the CSV file
                for encoding in ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1']:
                    try:
                        df = pd.read_csv(csv_file, encoding=encoding)
                        print(f"Loaded West Bengal CSV with {len(df)} rows using {encoding} encoding")
                        break
                    except UnicodeDecodeError:
                        continue
                else:
                    raise Exception("Could not decode CSV with any encoding")
            except Exception as e:
                print(f"Error loading {csv_file}: {e}")
                df = None

        # West Bengal center coordinates (South 24 Parganas)
        wb_center = [22.4707, 88.3617]

        # Process dealer data if available
        if df is not None and len(df) > 0 and 'latitude' in df.columns and 'longitude' in df.columns:
            valid_coords_df = df.dropna(subset=['latitude', 'longitude'])
            if len(valid_coords_df) > 0:
                center_lat = valid_coords_df['latitude'].mean()
                center_lon = valid_coords_df['longitude'].mean()
                wb_center = [center_lat, center_lon]

        # Create West Bengal map
        live_map = folium.Map(
            location=wb_center,
            zoom_start=11,
            tiles='OpenStreetMap'
        )

        # Add dealer markers if data is available
        dealers_added = 0
        if df is not None and len(df) > 0 and 'latitude' in df.columns and 'longitude' in df.columns:
            for idx, row in df.iterrows():
                try:
                    lat = row['latitude']
                    lon = row['longitude']

                    dealer_name = row.get('Name', f'Dealer {idx}')
                    address = row.get('Address', 'Address not available')
                    brand = row.get('Brand', 'Unknown')
                    pin = row.get('PIN', 'N/A')

                    dealer_info = f"""
                    <div style='width: 300px'>
                        <h4 style='color: #ff6600; margin: 0;'>🏪 {dealer_name}</h4>
                        <p><b>Brand:</b> {brand}<br>
                        <b>Address:</b> {address}<br>
                        <b>PIN:</b> {pin}<br>
                        <b>Coordinates:</b> {lat:.4f}, {lon:.4f}</p>
                    </div>
                    """

                    folium.Marker(
                        location=(lat, lon),
                        popup=folium.Popup(dealer_info, max_width=320),
                        icon=folium.Icon(color='red', icon='flash', prefix='fa')
                    ).add_to(live_map)
                    dealers_added += 1

                except Exception as e:
                    print(f"Error adding West Bengal dealer marker for row {idx}: {e}")
                    continue

        # Add South 24 Parganas reference marker
        s24p_center = [22.4707, 88.3617]
        folium.Marker(
            location=s24p_center,
            popup=folium.Popup(
                """
                <div style='width: 250px'>
                    <h4 style='color: #ff6600; margin: 0;'>📍 South 24 Parganas</h4>
                    <p><b>Location:</b> West Bengal<br>
                    <b>Geography:</b> South East<br>
                    <b>Type:</b> Commercial District<br>
                    <b>Specialties:</b> Electronics, Automotive</p>
                </div>
                """,
                max_width=300
            ),
            icon=folium.Icon(color='orange', icon='star', prefix='fa')
        ).add_to(live_map)

        # Add trade area circle
        folium.Circle(
            location=wb_center,
            radius=15000,  # 15km radius
            popup=f'West Bengal Trade Area Coverage<br>({dealers_added} dealers)',
            color='orange',
            fill=True,
            fillOpacity=0.1
        ).add_to(live_map)

        return live_map._repr_html_()

    except Exception as e:
        print(f"Error generating West Bengal map: {e}")
        return None

def get_lat_lon(pincode, geolocator):
    """Get latitude and longitude for a pincode"""
    try:
        location = geolocator.geocode(f"{pincode}, Delhi, India")
        if location:
            return location.latitude, location.longitude
    except Exception as e:
        print(f"Error geocoding {pincode}: {e}")
    return None, None

# Authentication Endpoints
@app.route('/api/auth/login', methods=['POST'])
def login():
    """Authenticate user and create session"""
    try:
        data = request.get_json()
        email = data.get('email', '').strip()
        password = data.get('password', '').strip()
        
        if not email or not password:
            return jsonify({'error': 'Email and password are required'}), 400
        
        # Validate credentials
        user = next((u for u in VALID_USERS if u['email'] == email and u['password'] == password), None)
        
        if user:
            session['authenticated'] = True
            session['user_email'] = email
            session['login_time'] = datetime.now().isoformat()
            
            return jsonify({
                'success': True,
                'message': 'Login successful',
                'user': {'email': email},
                'session_id': session.get('_id', str(uuid.uuid4()))
            })
        else:
            return jsonify({'error': 'Invalid credentials'}), 401
            
    except Exception as e:
        return jsonify({'error': f'Login failed: {str(e)}'}), 500

@app.route('/api/auth/logout', methods=['POST'])
def logout():
    """Logout user and clear session"""
    session.clear()
    return jsonify({'success': True, 'message': 'Logged out successfully'})

@app.route('/api/auth/check', methods=['GET'])
def check_auth():
    """Check if user is authenticated"""
    if session.get('authenticated'):
        return jsonify({
            'authenticated': True,
            'user': {'email': session.get('user_email')},
            'login_time': session.get('login_time')
        })
    return jsonify({'authenticated': False}), 401

# Map Utilities Endpoints
@app.route('/api/map/dealers', methods=['GET'])
def get_dealers():
    """Get dealer data with coordinates"""
    try:
        # Load dealer data
        dealers = []
        csv_files = [
            ('amaron_retailer_image.csv', 'Amaron', 'red'),
            ('exide_retailer_image.csv', 'Exide', 'blue'),
            ('tata_green_retailer_image.csv', 'Tata Green', 'green'),
            ('luminous_retailer_image.csv', 'Luminous', 'yellow')
        ]
        
        geolocator = Nominatim(user_agent="dealer-mapper-api")
        
        for csv_file, brand, color in csv_files:
            if os.path.exists(csv_file):
                df = pd.read_csv(csv_file)
                for _, row in df.iterrows():
                    lat, lon = get_lat_lon(row.get('PIN', ''), geolocator)
                    if lat and lon:
                        dealers.append({
                            'name': row.get('Name', ''),
                            'pin': row.get('PIN', ''),
                            'brand': brand,
                            'color': color,
                            'lat': lat,
                            'lon': lon,
                            'image': row.get('image', ''),
                            'address': row.get('Address', '')
                        })
                    sleep(0.5)  # Rate limiting for geocoding
        
        return jsonify({'dealers': dealers})
        
    except Exception as e:
        return jsonify({'error': f'Failed to load dealers: {str(e)}'}), 500

@app.route('/api/map/generate', methods=['POST'])
def generate_map():
    """Generate interactive map HTML"""
    try:
        data = request.get_json()
        center_lat = data.get('center_lat', 28.6139)
        center_lon = data.get('center_lon', 77.2090)
        zoom = data.get('zoom', 11)
        
        # Create map
        map_obj = folium.Map(location=[center_lat, center_lon], zoom_start=zoom)
        
        # Get dealers data
        dealers_response = get_dealers()
        if dealers_response[1] == 200:  # Success
            dealers_data = json.loads(dealers_response[0].data)
            dealers = dealers_data.get('dealers', [])
            
            # Add markers for each dealer
            for dealer in dealers:
                popup_html = f"""
                    <b>{dealer['brand']} Dealer:</b> {dealer['name']}<br>
                    <b>PIN:</b> {dealer['pin']}<br>
                    <b>Address:</b> {dealer.get('address', 'N/A')}<br>
                """
                
                if dealer.get('image'):
                    image_path = f"images/{dealer['image']}"
                    if os.path.exists(image_path):
                        popup_html += f"<img src='images/{dealer['image']}' width='200' height='150'>"
                
                folium.Marker(
                    location=[dealer['lat'], dealer['lon']],
                    popup=folium.Popup(popup_html, max_width=250),
                    icon=folium.Icon(color=dealer['color'], icon='info-sign')
                ).add_to(map_obj)
        
        # Add layer control
        folium.LayerControl().add_to(map_obj)
        
        return jsonify({'map_html': map_obj._repr_html_()})
        
    except Exception as e:
        return jsonify({'error': f'Failed to generate map: {str(e)}'}), 500

# Interactive Map Endpoints
@app.route('/api/images/gallery', methods=['GET'])
def get_image_gallery():
    """Get images for gallery display"""
    try:
        state = request.args.get('state', 'Delhi')
        brand_filter = request.args.get('brand', '')
        show_competitors = request.args.get('show_competitors', 'false').lower() == 'true'

        # Map state names to folder names
        state_folder_map = {
            'Delhi(NCT)': 'Delhi',
            'Delhi': 'Delhi',
            'West Bengal': 'West Bengal'
        }

        folder_name = state_folder_map.get(state, state)
        images = []
        base_path = f"interactive_images/{folder_name}"

        print(f"Image gallery request: state='{state}', folder='{folder_name}', brand='{brand_filter}', path='{base_path}'")
        print(f"Path exists: {os.path.exists(base_path)}")
        
        if os.path.exists(base_path):
            for root, dirs, files in os.walk(base_path):
                folder_name = os.path.basename(root)
                print(f"Processing folder: {folder_name}")

                for file in files:
                    if file.lower().endswith(('.jpg', '.jpeg', '.png', '.gif', '.bmp')):
                        file_path = os.path.join(root, file)

                        # Brand filtering - handle folder naming patterns
                        if brand_filter and brand_filter != "All Brands":
                            # Check if brand name is in folder name (e.g., "YusufSaraiPhotos-Amaron")
                            brand_in_folder = brand_filter.lower() in folder_name.lower()
                            print(f"  Brand filter '{brand_filter}' in folder '{folder_name}': {brand_in_folder}")
                            if not brand_in_folder:
                                continue
                            
                        if not show_competitors and 'competitor' in folder_name.lower():
                            continue
                        
                        # Extract brand name from folder name (e.g., "YusufSaraiPhotos-Amaron" -> "Amaron")
                        brand_name = folder_name
                        if '-' in folder_name:
                            brand_name = folder_name.split('-')[-1]  # Get the part after the last dash

                        # Generate coordinates (mock data for now)
                        coordinates = f"28.{55 + hash(file) % 10:02d}, 77.{20 + hash(file) % 30:02d}"

                        # Encode image to base64
                        img_base64 = encode_image_to_base64(file_path)
                        if img_base64:
                            images.append({
                                'name': file,
                                'folder': folder_name,
                                'brand': brand_name,
                                'base64': img_base64,
                                'path': file_path,
                                'state': state,
                                'coordinates': coordinates,
                                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                            })
        else:
            print(f"Base path does not exist: {base_path}")

        print(f"Total images found: {len(images)}")
        return jsonify({'images': images, 'total': len(images)})
        
    except Exception as e:
        return jsonify({'error': f'Failed to load images: {str(e)}'}), 500

# Mimo Form Endpoints
@app.route('/api/form/submit', methods=['POST'])
def submit_mimo_form():
    """Submit Mimo form data"""
    try:
        data = request.get_json()
        
        # Generate ticket ID
        ticket_id = f"MIMO-{datetime.now().strftime('%Y%m%d')}-{str(uuid.uuid4())[:8].upper()}"
        
        # Add metadata
        form_data = {
            'ticket_id': ticket_id,
            'submitted_at': datetime.now().isoformat(),
            'user_email': session.get('user_email', 'anonymous'),
            'form_data': data
        }
        
        # Save to file (in production, use database)
        forms_dir = 'submitted_forms'
        os.makedirs(forms_dir, exist_ok=True)
        
        with open(f"{forms_dir}/{ticket_id}.json", 'w') as f:
            json.dump(form_data, f, indent=2)
        
        return jsonify({
            'success': True,
            'ticket_id': ticket_id,
            'message': 'Form submitted successfully'
        })
        
    except Exception as e:
        return jsonify({'error': f'Form submission failed: {str(e)}'}), 500

@app.route('/api/form/tickets', methods=['GET'])
def get_form_tickets():
    """Get list of submitted form tickets"""
    try:
        forms_dir = 'submitted_forms'
        tickets = []
        
        if os.path.exists(forms_dir):
            for filename in os.listdir(forms_dir):
                if filename.endswith('.json'):
                    with open(os.path.join(forms_dir, filename), 'r') as f:
                        ticket_data = json.load(f)
                        tickets.append({
                            'ticket_id': ticket_data['ticket_id'],
                            'submitted_at': ticket_data['submitted_at'],
                            'user_email': ticket_data['user_email']
                        })
        
        return jsonify({'tickets': tickets})
        
    except Exception as e:
        return jsonify({'error': f'Failed to load tickets: {str(e)}'}), 500

# Data Endpoints
@app.route('/api/data/csv', methods=['GET'])
def get_csv_data():
    """Get CSV data for dashboard"""
    try:
        filename = request.args.get('file', 'DataValues-Dashboard-CSV.csv')
        
        if os.path.exists(filename):
            df = pd.read_csv(filename)
            return jsonify({
                'data': df.to_dict('records'),
                'columns': df.columns.tolist(),
                'total_rows': len(df)
            })
        else:
            return jsonify({'error': 'File not found'}), 404
            
    except Exception as e:
        return jsonify({'error': f'Failed to load CSV: {str(e)}'}), 500

# Detailed Analytics Endpoint - Matching Streamlit's detailed analytics functionality
@app.route('/api/analytics/detailed', methods=['GET'])
def get_detailed_analytics():
    """Generate detailed analytics HTML page like Streamlit version"""
    try:
        # Read the CSV file
        csv_file = 'DataValues-Dashboard-CSV.csv'
        if not os.path.exists(csv_file):
            return jsonify({'error': 'DataValues-Dashboard-CSV.csv file not found'}), 404

        import pandas as pd
        csv_df = pd.read_csv(csv_file)

        # Convert Values column to float type and handle NaN values
        csv_df['Values'] = pd.to_numeric(csv_df['Values'], errors='coerce')
        # Only filter out NaN values, keep zero values
        csv_df = csv_df[csv_df['Values'].notna()]

        # Format Values column for display
        def format_value(val):
            if pd.isna(val):
                return ''
            if val == 0:
                return '0'
            elif val < 1 and val > 0:
                formatted = f"{val:.5f}"
            elif val >= 1000:
                formatted = f"{val:.0f}"
            else:
                formatted = f"{val:.3f}"

            # Remove excessive trailing zeros, keep max one zero after decimal
            if '.' in formatted and val != 0:
                formatted = formatted.rstrip('0')
                if formatted.endswith('.'):
                    formatted += '0'
                elif '.' not in formatted:
                    formatted += '.0'

            return formatted

        # Apply formatting to Values column for display
        csv_df['Values'] = csv_df['Values'].apply(format_value)

        # Get unique categories and subcategories for filters
        master_categories = sorted(csv_df['Master Category Name'].unique())
        all_subcategories = sorted(csv_df['Sub category'].unique())

        # Create subcategory mapping for dynamic filtering
        subcategory_mapping = {}
        for cat in master_categories:
            subcategory_mapping[cat] = sorted(csv_df[csv_df['Master Category Name'] == cat]['Sub category'].unique())

        return jsonify({
            'success': True,
            'data': csv_df.to_dict('records'),
            'master_categories': master_categories,
            'all_subcategories': all_subcategories,
            'subcategory_mapping': subcategory_mapping,
            'total_records': len(csv_df)
        })

    except Exception as e:
        return jsonify({'error': f'Failed to generate detailed analytics: {str(e)}'}), 500

# Detailed Analytics HTML Page - Serves the full HTML page like Streamlit
@app.route('/detailed-analytics')
def detailed_analytics_page():
    """Serve the detailed analytics HTML page"""
    try:
        # Read the CSV file
        csv_file = 'DataValues-Dashboard-CSV.csv'
        if not os.path.exists(csv_file):
            return "<h1>Error: DataValues-Dashboard-CSV.csv file not found</h1>", 404

        import pandas as pd
        from datetime import datetime

        csv_df = pd.read_csv(csv_file)

        # Convert Values column to float type and handle NaN values
        csv_df['Values'] = pd.to_numeric(csv_df['Values'], errors='coerce')
        csv_df = csv_df[csv_df['Values'].notna()]

        # Format Values column for display
        def format_value(val):
            if pd.isna(val):
                return ''
            if val == 0:
                return '0'
            elif val < 1 and val > 0:
                formatted = f"{val:.5f}"
            elif val >= 1000:
                formatted = f"{val:.0f}"
            else:
                formatted = f"{val:.3f}"

            if '.' in formatted and val != 0:
                formatted = formatted.rstrip('0')
                if formatted.endswith('.'):
                    formatted += '0'
                elif '.' not in formatted:
                    formatted += '.0'

            return formatted

        csv_df['Values'] = csv_df['Values'].apply(format_value)

        # Get unique categories and subcategories for filters
        master_categories = sorted(csv_df['Master Category Name'].unique())
        all_subcategories = sorted(csv_df['Sub category'].unique())

        # Create subcategory mapping for dynamic filtering
        subcategory_mapping = {}
        for cat in master_categories:
            subcategory_mapping[cat] = sorted(csv_df[csv_df['Master Category Name'] == cat]['Sub category'].unique())

        # Generate timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Create the HTML page (matching Streamlit's design)
        html_content = generate_detailed_analytics_html(csv_df, master_categories, all_subcategories, subcategory_mapping, timestamp)

        return html_content

    except Exception as e:
        return f"<h1>Error generating detailed analytics: {str(e)}</h1>", 500

def generate_detailed_analytics_html(csv_df, master_categories, all_subcategories, subcategory_mapping, timestamp):
    """Generate the detailed analytics HTML page matching Streamlit's design"""

    # Encode logos for HTML
    stonesbury_logo_html = ""
    mimo_logo_html = ""
    try:
        from PIL import Image
        from io import BytesIO

        stonesbury_path = "logo/Stonesbury-logo.png"
        adobe_path = "logo/Adobe Express - file.png"

        if os.path.exists(stonesbury_path):
            stonesbury_img = Image.open(stonesbury_path)
            buffered = BytesIO()
            stonesbury_img.save(buffered, format="PNG")
            stonesbury_base64 = base64.b64encode(buffered.getvalue()).decode()
            stonesbury_logo_html = f'<img src="data:image/png;base64,{stonesbury_base64}" alt="Stonesbury Logo" style="height: 45px; width: auto; border: 1px solid #ddd; border-radius: 5px; padding: 5px; background: white;">'

        if os.path.exists(adobe_path):
            adobe_img = Image.open(adobe_path)
            buffered = BytesIO()
            adobe_img.save(buffered, format="PNG")
            adobe_base64 = base64.b64encode(buffered.getvalue()).decode()
            mimo_logo_html = f'<img src="data:image/png;base64,{adobe_base64}" alt="Mimo Logo" style="height: 45px; width: auto; border: 1px solid #ddd; border-radius: 5px; padding: 5px; background: white;">'

    except Exception:
        # If logos fail to load, we'll use text fallbacks
        stonesbury_logo_html = '<div style="height: 45px; padding: 10px; background: #ff6600; color: white; border-radius: 5px; font-weight: bold;">Stonesbury</div>'
        mimo_logo_html = '<div style="height: 45px; padding: 10px; background: #333; color: white; border-radius: 5px; font-weight: bold;">Mimo</div>'

    # Create the HTML template
    html_template = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>Stonesburry Mimo Analytics - Detailed Data - {timestamp}</title>
        <style>
            body {{
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                margin: 20px;
                background-color: white;
            }}
            h1 {{
                color: #ff6600;
                text-align: center;
                margin-bottom: 15px;
                font-size: 1.8em;
            }}
            .header-container {{
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 20px;
                padding: 10px;
                border-bottom: 2px solid #ff6600;
            }}
            .filter-container {{
                background: white;
                padding: 12px;
                border-radius: 8px;
                border: 1px solid #ff6600;
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                margin-bottom: 12px;
                display: flex;
                gap: 10px;
                align-items: end;
                flex-wrap: wrap;
                justify-content: space-between;
            }}
            .filter-group {{
                display: flex;
                flex-direction: column;
                min-width: 180px;
            }}
            .filter-group label {{
                color: #ff6600;
                font-weight: bold;
                margin-bottom: 5px;
                font-size: 14px;
            }}
            .filter-group select {{
                padding: 10px;
                border: 2px solid #ff6600;
                border-radius: 5px;
                font-size: 14px;
                background-color: white;
                color: #333;
            }}
            .get-button {{
                padding: 12px 25px;
                background-color: #ff6600;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
                cursor: pointer;
                transition: background-color 0.3s ease;
                height: fit-content;
                min-width: 80px;
            }}
            .get-button:hover {{
                background-color: #e55a00;
            }}
            .clear-button {{
                padding: 12px 25px;
                background-color: #6c757d;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
                cursor: pointer;
                transition: background-color 0.3s ease;
                height: fit-content;
                min-width: 80px;
            }}
            .clear-button:hover {{
                background-color: #5a6268;
            }}
            .table-container {{
                background: white;
                border-radius: 8px;
                border: 1px solid #ff6600;
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                overflow: hidden;
                margin: 12px 0;
            }}
            table {{
                width: 100%;
                border-collapse: collapse;
                font-size: 15px;
                table-layout: fixed;
            }}
            th {{
                background-color: #ff6600;
                color: white;
                padding: 10px 12px;
                text-align: left;
                font-weight: bold;
                position: sticky;
                top: 0;
                z-index: 10;
                min-width: 120px;
                font-size: 14px;
            }}
            td {{
                padding: 8px 12px;
                border-bottom: 1px solid #eee;
                vertical-align: top;
                min-width: 120px;
                word-wrap: break-word;
                font-size: 14px;
                background-color: white;
            }}
            .no-results {{
                text-align: center;
                padding: 40px;
                color: #666;
            }}
            /* Action Items column styling */
            th:last-child {{
                width: 120px;
                min-width: 120px;
                text-align: center;
            }}
            .action-cell {{
                text-align: center !important;
                padding: 8px 12px !important;
                width: 130px;
                min-width: 130px;
                vertical-align: middle !important;
                display: table-cell !important;
                visibility: visible !important;
                background-color: white !important;
            }}
            .action-button {{
                background: linear-gradient(135deg, #ff6600, #ff8533);
                color: white;
                border: none;
                padding: 8px 12px;
                border-radius: 5px;
                cursor: pointer;
                font-size: 12px;
                font-weight: bold;
                transition: all 0.3s ease;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                min-width: 90px;
            }}
            .action-button:hover {{
                background: linear-gradient(135deg, #e55a00, #ff6600);
                transform: translateY(-1px);
                box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            }}
            .action-button:active {{
                transform: translateY(0);
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }}
        </style>
    </head>
    <body>
        <div class="header-container">
            <div>{stonesbury_logo_html}</div>
            <h1>Stonesburry Mimo Analytics - Detailed Data</h1>
            <div>{mimo_logo_html}</div>
        </div>

        <div class="filter-container">
            <div class="filter-group">
                <label for="masterCategoryFilter">Master Category:</label>
                <select id="masterCategoryFilter">
                    <option value="">All Categories</option>
                    {generate_category_options(master_categories)}
                </select>
            </div>

            <div class="filter-group">
                <label for="subCategoryFilter">Sub Category:</label>
                <select id="subCategoryFilter">
                    <option value="">All Sub-categories</option>
                    {generate_subcategory_options(all_subcategories)}
                </select>
            </div>

            <button class="get-button" onclick="applyFilters()">Go</button>
            <button class="clear-button" onclick="clearFilters()">Clear</button>
        </div>

        <div class="table-container">
            <div id="tableContent" style="display: none;">
                <!-- Table will be populated when filters are applied -->
            </div>
            <div id="noResults" class="no-results" style="display: block;">
                <h3>Please select filters to view data</h3>
                <p>Choose a Master Category and/or Sub Category from the filters above, then click 'Go' to display the data.</p>
            </div>
        </div>

        <script>
            // Store original data for filtering
            const originalData = {csv_df.to_json(orient='records')};
            const subcategoryMapping = {json.dumps(subcategory_mapping)};

            // Function to open Mimo form with parameters
            function openSampleForm(kpi, category, subcategory, row) {{
                console.log('Opening Mimo form for KPI:', kpi);

                // Encode parameters for URL
                const params = new URLSearchParams({{
                    kpi: kpi || '',
                    category: category || '',
                    subcategory: subcategory || '',
                    row: row || ''
                }});

                // Construct URL with parameters - using Flask/React Mimo form
                const baseUrl = 'http://localhost:5173'; // React app URL
                const fullUrl = `${{baseUrl}}?tab=form&${{params.toString()}}`;

                console.log('Opening Mimo form with URL:', fullUrl);

                // Open in new window with parameters
                try {{
                    const newWindow = window.open(fullUrl, '_blank');
                    if (newWindow) {{
                        console.log('Successfully opened Mimo form with parameters');
                    }} else {{
                        throw new Error('Popup blocked');
                    }}
                }} catch (e) {{
                    console.log('Popup blocked or failed:', e);

                    // Alternative: show instructions with the full URL
                    alert(
                        'Please manually open the Mimo form:\\n\\n' +
                        '1. Open a new browser tab\\n' +
                        '2. Copy and paste this URL:\\n' +
                        `   ${{fullUrl}}\\n\\n` +
                        'Or use the dashboard to navigate to the Mimo Form.'
                    );
                }}
            }}

            // Update subcategory dropdown based on master category selection
            document.getElementById('masterCategoryFilter').addEventListener('change', function() {{
                const selectedCategory = this.value;
                const subCategorySelect = document.getElementById('subCategoryFilter');

                // Clear existing options
                subCategorySelect.innerHTML = '<option value="">All Sub-categories</option>';

                if (selectedCategory && subcategoryMapping[selectedCategory]) {{
                    subcategoryMapping[selectedCategory].forEach(subcat => {{
                        const option = document.createElement('option');
                        option.value = subcat;
                        option.textContent = subcat;
                        subCategorySelect.appendChild(option);
                    }});
                }} else {{
                    // Show all subcategories if no master category selected
                    const allSubcats = {json.dumps(all_subcategories)};
                    allSubcats.forEach(subcat => {{
                        const option = document.createElement('option');
                        option.value = subcat;
                        option.textContent = subcat;
                        subCategorySelect.appendChild(option);
                    }});
                }}
            }});

            // Apply filters function
            function applyFilters() {{
                const masterCategory = document.getElementById('masterCategoryFilter').value;
                const subCategory = document.getElementById('subCategoryFilter').value;

                // Only show data if at least one filter is selected
                if (!masterCategory && !subCategory) {{
                    document.getElementById('tableContent').style.display = 'none';
                    document.getElementById('noResults').style.display = 'block';
                    document.getElementById('noResults').innerHTML = '<h3>Please select filters to view data</h3><p>Choose a Master Category and/or Sub Category from the filters above to display the data.</p>';
                    return;
                }}

                let filteredData = originalData;

                // Filter out empty/null values
                filteredData = filteredData.filter(row => {{
                    const value = row['Values'];
                    return value !== null && value !== undefined && value !== '';
                }});

                if (masterCategory) {{
                    filteredData = filteredData.filter(row => row['Master Category Name'] === masterCategory);
                }}

                if (subCategory) {{
                    filteredData = filteredData.filter(row => row['Sub category'] === subCategory);
                }}

                if (filteredData.length === 0) {{
                    document.getElementById('tableContent').style.display = 'none';
                    document.getElementById('noResults').style.display = 'block';
                    document.getElementById('noResults').innerHTML = '<h3>No data found</h3><p>No records match the selected filters. Please try different filter combinations.</p>';
                    return;
                }}

                // Show table and hide no results message
                document.getElementById('tableContent').style.display = 'block';
                document.getElementById('noResults').style.display = 'none';

                // Generate table
                generateTable(filteredData);
            }}

            // Generate table function
            function generateTable(data) {{
                const tableContent = document.getElementById('tableContent');

                if (data.length === 0) {{
                    tableContent.innerHTML = '<p class="no-results">No data available for the selected filters.</p>';
                    return;
                }}

                // Get all unique columns from the data, ensuring Action Items is last
                const allColumns = [...new Set(data.flatMap(row => Object.keys(row)))];
                const columns = allColumns.filter(col => col !== 'Action Items');
                columns.push('Action Items'); // Add Action Items as the last column

                let tableHTML = '<table><thead><tr>';
                columns.forEach(col => {{
                    tableHTML += `<th>${{col}}</th>`;
                }});
                tableHTML += '</tr></thead><tbody>';

                data.forEach((row, index) => {{
                    const rowClass = index % 2 === 0 ? '' : 'even';
                    tableHTML += `<tr class="${{rowClass}}">`;
                    columns.forEach(col => {{
                        if (col === 'Action Items') {{
                            // Create unique button for each row with KPI Number and category info
                            const kpiNumber = row['KPI Number'] || index + 1;
                            const category = row['Master Category Name'] || 'Unknown';
                            const subCategory = row['Sub category'] || 'Unknown';
                            const buttonId = `action_btn_${{kpiNumber}}_${{index}}`;

                            tableHTML += `<td class="action-cell">
                                <button class="action-button"
                                        id="${{buttonId}}"
                                        onclick="openSampleForm('${{kpiNumber}}', '${{category}}', '${{subCategory}}', '${{index}}')">
                                    View Details
                                </button>
                            </td>`;
                        }} else {{
                            const value = row[col] || '';
                            tableHTML += `<td>${{value}}</td>`;
                        }}
                    }});
                    tableHTML += '</tr>';
                }});

                tableHTML += '</tbody></table>';
                tableContent.innerHTML = tableHTML;
            }}

            // Clear filters function
            function clearFilters() {{
                document.getElementById('masterCategoryFilter').value = '';
                document.getElementById('subCategoryFilter').value = '';
                document.getElementById('tableContent').style.display = 'none';
                document.getElementById('noResults').style.display = 'block';
                document.getElementById('noResults').innerHTML = '<h3>Please select filters to view data</h3><p>Choose a Master Category and/or Sub Category from the filters above, then click \\'Go\\' to display the data.</p>';
            }}
        </script>
    </body>
    </html>
    """

    return html_template

def generate_category_options(categories):
    """Generate HTML options for categories"""
    return ''.join([f'<option value="{cat}">{cat}</option>' for cat in categories])

def generate_subcategory_options(subcategories):
    """Generate HTML options for subcategories"""
    return ''.join([f'<option value="{subcat}">{subcat}</option>' for subcat in subcategories])

# Analytics Endpoints - Matching Streamlit functionality
@app.route('/api/analytics/dashboard', methods=['GET'])
def get_dashboard_data():
    """Get dashboard analytics data"""
    try:
        # Load main dashboard CSV
        csv_file = 'DataValues-Dashboard-CSV.csv'
        if os.path.exists(csv_file):
            df = pd.read_csv(csv_file)

            # Calculate analytics similar to Streamlit app
            analytics = {
                'total_records': len(df),
                'unique_states': df['State'].nunique() if 'State' in df.columns else 0,
                'unique_geographies': df['Geography'].nunique() if 'Geography' in df.columns else 0,
                'data_summary': df.describe().to_dict() if not df.empty else {},
                'column_info': {
                    'columns': df.columns.tolist(),
                    'dtypes': df.dtypes.astype(str).to_dict()
                }
            }

            return jsonify(analytics)
        else:
            return jsonify({'error': 'Dashboard data file not found'}), 404

    except Exception as e:
        return jsonify({'error': f'Failed to load dashboard data: {str(e)}'}), 500

@app.route('/api/analytics/geographic', methods=['GET'])
def get_geographic_data():
    """Get geographic analytics data"""
    try:
        state = request.args.get('state', 'Delhi(NCT)')
        geography = request.args.get('geography', 'South Central')
        locality = request.args.get('locality', 'Yusuf Sarai')

        # Validate geographic combination
        valid_combinations = [
            ["Delhi(NCT)", "South Central", "Yusuf Sarai"],
            ["West Bengal", "East", "South 24 Parganas"]
        ]

        current_combination = [state, geography, locality]
        is_valid = any(combo == current_combination for combo in valid_combinations)

        if not is_valid:
            return jsonify({
                'valid': False,
                'message': 'Libraries are not connected',
                'valid_combinations': valid_combinations
            })

        # Load relevant data for the geographic combination
        data = {
            'valid': True,
            'state': state,
            'geography': geography,
            'locality': locality,
            'analytics': {
                'population': 125000 if state == "Delhi(NCT)" else 89000,
                'market_size': 'Large' if state == "Delhi(NCT)" else 'Medium',
                'growth_rate': 12.5 if state == "Delhi(NCT)" else 8.3
            }
        }

        return jsonify(data)

    except Exception as e:
        return jsonify({'error': f'Failed to load geographic data: {str(e)}'}), 500

@app.route('/api/analytics/excel', methods=['GET'])
def get_excel_data():
    """Get Excel file data - matching Streamlit's Excel processing"""
    try:
        excel_file = 'YusufSaraiAmaronCentre-FinalCSV.xlsx'
        if os.path.exists(excel_file):
            # Read Excel file similar to Streamlit app
            df = pd.read_excel(excel_file)

            return jsonify({
                'data': df.to_dict('records'),
                'columns': df.columns.tolist(),
                'total_rows': len(df),
                'file_info': {
                    'filename': excel_file,
                    'sheet_names': pd.ExcelFile(excel_file).sheet_names
                }
            })
        else:
            return jsonify({'error': 'Excel file not found'}), 404

    except Exception as e:
        return jsonify({'error': f'Failed to load Excel data: {str(e)}'}), 500

# GeoIQ Images Endpoint - Matching Streamlit's image processing
@app.route('/api/geoiq/images', methods=['GET'])
def get_geoiq_images():
    """Get GeoIQ analysis images"""
    try:
        images_dir = 'GeoIQimages'
        images = []

        if os.path.exists(images_dir):
            for filename in os.listdir(images_dir):
                if filename.lower().endswith(('.png', '.jpg', '.jpeg', '.gif')):
                    image_path = os.path.join(images_dir, filename)
                    img_base64 = encode_image_to_base64(image_path)
                    if img_base64:
                        images.append({
                            'name': filename,
                            'base64': img_base64,
                            'path': image_path
                        })

        return jsonify({'images': images, 'total': len(images)})

    except Exception as e:
        return jsonify({'error': f'Failed to load GeoIQ images: {str(e)}'}), 500

# Category Images Endpoint - Matching Streamlit's item image functionality
@app.route('/api/images/<filename>', methods=['GET'])
def get_category_image(filename):
    """Get category/subcategory images"""
    try:
        # Try different image directories based on state
        image_dirs = [
            'Items/YusufSaraiItemPrice',
            'Items/South24ParganasItemPrices',
            'GeoIQimages/YusufSaraiGEOIQImages',
            'GeoIQimages/South24ParganasGEOIQImages',
            'interactive_images'
        ]

        for image_dir in image_dirs:
            if os.path.exists(image_dir):
                image_path = os.path.join(image_dir, filename)
                if os.path.exists(image_path):
                    img_base64 = encode_image_to_base64(image_path)
                    if img_base64:
                        return jsonify({
                            'success': True,
                            'base64': img_base64,
                            'filename': filename,
                            'path': image_path
                        })

        return jsonify({'error': f'Image {filename} not found'}), 404

    except Exception as e:
        return jsonify({'error': f'Failed to load image: {str(e)}'}), 500

# Interactive Images Endpoint - Matching Streamlit's interactive gallery
@app.route('/api/interactive/images', methods=['GET'])
def get_interactive_images():
    """Get interactive gallery images with brand filtering"""
    try:
        search_query = request.args.get('search', '')
        state = request.args.get('state', 'Delhi(NCT)')
        show_competitors = request.args.get('show_competitors', 'false').lower() == 'true'

        images_dir = 'interactive_images'
        images = []

        if os.path.exists(images_dir):
            # Walk through all subdirectories
            for root, dirs, files in os.walk(images_dir):
                for filename in files:
                    if filename.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.bmp')):
                        image_path = os.path.join(root, filename)

                        # Extract brand from folder structure or filename
                        brand = extract_brand_from_path(image_path)

                        # Filter by search query if provided
                        if search_query and not show_competitors:
                            if search_query.lower() not in brand.lower() and search_query.lower() not in filename.lower():
                                continue

                        img_base64 = encode_image_to_base64(image_path)
                        if img_base64:
                            images.append({
                                'name': filename,
                                'brand': brand,
                                'base64': img_base64,
                                'path': image_path,
                                'state': state,
                                'folder': os.path.basename(root),
                                'timestamp': 'Recent',
                                'coordinates': 'Unknown',
                                'location': 'Unknown'
                            })

        return jsonify({'images': images, 'total': len(images)})

    except Exception as e:
        return jsonify({'error': f'Failed to load interactive images: {str(e)}'}), 500

# Analytics Images Endpoint - Matching Streamlit's analytics image functionality
@app.route('/api/analytics/image/<image_name>', methods=['GET'])
def get_analytics_image(image_name):
    """Get analytics images based on state and image name"""
    try:
        state = request.args.get('state', 'Delhi(NCT)')
        image_type = request.args.get('type', 'analytics')  # 'analytics' or 'category'

        if image_type == 'category':
            # Handle category images (Items folder)
            state_folder_mapping = {
                'Delhi(NCT)': 'YusufSaraiItemPrice',
                'West Bengal': 'South24ParganasItemPrices'
            }
            folder_name = state_folder_mapping.get(state, 'YusufSaraiItemPrice')
            image_dir = f'Items/{folder_name}'
        else:
            # Handle analytics images (GeoIQimages folder)
            state_folder_mapping = {
                'Delhi(NCT)': 'YusufSaraiGEOIQImages',
                'West Bengal': 'South24ParganasGEOIQImages'
            }
            folder_name = state_folder_mapping.get(state, 'YusufSaraiGEOIQImages')
            image_dir = f'GeoIQimages/{folder_name}'

        # Try to find the image file
        image_filename = f"{image_name}.png"
        image_path = os.path.join(image_dir, image_filename)

        if os.path.exists(image_path):
            img_base64 = encode_image_to_base64(image_path)
            if img_base64:
                return jsonify({
                    'success': True,
                    'base64': img_base64,
                    'filename': image_filename,
                    'path': image_path,
                    'state': state,
                    'folder': folder_name
                })

        # If not found, try alternative locations
        alternative_dirs = [
            'GeoIQimages',
            f'GeoIQimages/{state}',
            'interactive_images',
            f'interactive_images/{state}'
        ]

        for alt_dir in alternative_dirs:
            if os.path.exists(alt_dir):
                alt_path = os.path.join(alt_dir, image_filename)
                if os.path.exists(alt_path):
                    img_base64 = encode_image_to_base64(alt_path)
                    if img_base64:
                        return jsonify({
                            'success': True,
                            'base64': img_base64,
                            'filename': image_filename,
                            'path': alt_path,
                            'state': state,
                            'folder': os.path.basename(alt_dir)
                        })

        return jsonify({
            'success': False,
            'error': f'Analytics image "{image_filename}" not found for state "{state}"'
        }), 404

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'Failed to load analytics image: {str(e)}'
        }), 500



def extract_brand_from_path(image_path):
    """Extract brand name from image path or filename"""
    path_parts = image_path.split(os.sep)
    filename = os.path.basename(image_path)

    # Common brand names to look for
    brands = ['amaron', 'exide', 'tata', 'luminous', 'microtek', 'su-kam']

    # Check folder names
    for part in path_parts:
        for brand in brands:
            if brand.lower() in part.lower():
                return brand.capitalize()

    # Check filename
    for brand in brands:
        if brand.lower() in filename.lower():
            return brand.capitalize()

    return 'Unknown Brand'

# Health Check
@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'version': '1.0.0'
    })

# Interactive Map Generation Endpoint - Matching Streamlit's map functionality
@app.route('/api/interactive-map/generate', methods=['GET'])
def generate_interactive_map_endpoint():
    """Generate interactive map HTML for the specified state"""
    try:
        state = request.args.get('state', 'Delhi(NCT)')

        # Import folium for map generation
        try:
            import folium
            import pandas as pd
        except ImportError:
            return jsonify({
                'success': False,
                'error': 'Required packages (folium, pandas) not available'
            }), 500

        if state == "Delhi(NCT)":
            # Generate Delhi map
            map_html = generate_delhi_map()
        elif state == "West Bengal":
            # Generate West Bengal map
            map_html = generate_west_bengal_map()
        else:
            return jsonify({
                'success': False,
                'error': f'Map generation not supported for state: {state}'
            }), 400

        if map_html:
            return jsonify({
                'success': True,
                'html': map_html,
                'state': state
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Failed to generate map HTML'
            }), 500

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'Failed to generate map: {str(e)}'
        }), 500

@app.route('/api/test/category/<image_name>', methods=['GET'])
def test_category_image(image_name):
    """Test category image endpoint"""
    return jsonify({
        'success': True,
        'message': f'Test endpoint working for {image_name}',
        'timestamp': datetime.now().isoformat()
    })

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
