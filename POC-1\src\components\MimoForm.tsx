import React, { useState } from 'react';
import { Send, CheckCircle, AlertCircle, User, Phone, Mail, MapPin, Calendar, FileText } from 'lucide-react';

interface FormData {
  serviceProvider: string;
  kpi: string;
  category: string;
  subcategory: string;
  row: string;
  validationType: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  state: string;
  pincode: string;
  visitDate: string;
  visitTime: string;
  comments: string;
}

interface MimoFormProps {
  onSubmit: (data: FormData) => Promise<{ success: boolean; ticketId?: string; error?: string }>;
  initialData?: Partial<FormData>;
}

export const MimoForm: React.FC<MimoFormProps> = ({ onSubmit, initialData = {} }) => {
  const [formData, setFormData] = useState<FormData>({
    serviceProvider: 'Mimo',
    kpi: '',
    category: '',
    subcategory: '',
    row: '',
    validationType: '',
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    state: '',
    pincode: '',
    visitDate: '',
    visitTime: '',
    comments: '',
    ...initialData
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitResult, setSubmitResult] = useState<{ success: boolean; ticketId?: string; error?: string } | null>(null);
  const [errors, setErrors] = useState<Partial<Record<keyof FormData, string>>>({});

  const validateForm = (): boolean => {
    const newErrors: Partial<Record<keyof FormData, string>> = {};

    // Required field validation
    const requiredFields: (keyof FormData)[] = [
      'serviceProvider', 'kpi', 'category', 'firstName', 'lastName', 
      'email', 'phone', 'address', 'city', 'state', 'pincode'
    ];

    requiredFields.forEach(field => {
      if (!formData[field].trim()) {
        newErrors[field] = `${field.charAt(0).toUpperCase() + field.slice(1)} is required`;
      }
    });

    // Email validation
    if (formData.email && !/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    // Phone validation
    if (formData.phone && !/^\d{10}$/.test(formData.phone.replace(/\D/g, ''))) {
      newErrors.phone = 'Please enter a valid 10-digit phone number';
    }

    // Pincode validation
    if (formData.pincode && !/^\d{6}$/.test(formData.pincode)) {
      newErrors.pincode = 'Please enter a valid 6-digit pincode';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setIsSubmitting(true);
    setSubmitResult(null);

    try {
      const result = await onSubmit(formData);
      setSubmitResult(result);
      
      if (result.success) {
        // Reset form on successful submission
        setFormData({
          serviceProvider: 'Mimo',
          kpi: '',
          category: '',
          subcategory: '',
          row: '',
          validationType: '',
          firstName: '',
          lastName: '',
          email: '',
          phone: '',
          address: '',
          city: '',
          state: '',
          pincode: '',
          visitDate: '',
          visitTime: '',
          comments: ''
        });
      }
    } catch (error) {
      setSubmitResult({ success: false, error: 'Submission failed. Please try again.' });
    } finally {
      setIsSubmitting(false);
    }
  };

  const InputField: React.FC<{
    label: string;
    field: keyof FormData;
    type?: string;
    placeholder?: string;
    required?: boolean;
    icon?: React.ReactNode;
    options?: string[];
  }> = ({ label, field, type = 'text', placeholder, required = false, icon, options }) => (
    <div style={{ marginBottom: '20px' }}>
      <label
        className="block text-sm font-medium text-gray-700 mb-2"
        style={{
          fontSize: '16px',
          fontWeight: '500',
          color: '#374151',
          marginBottom: '8px',
          display: 'block'
        }}
      >
        {label} {required && <span className="text-red-500">*</span>}
      </label>
      <div className="relative">
        {icon && (
          <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
            {icon}
          </div>
        )}
        {options ? (
          <select
            value={formData[field]}
            onChange={(e) => handleInputChange(field, e.target.value)}
            className={`w-full ${icon ? 'pl-10' : 'pl-4'} pr-4 py-3 border rounded-md focus:ring-2 focus:ring-orange-500 outline-none transition-colors ${
              errors[field] ? 'border-red-500' : 'border-gray-300'
            }`}
            style={{
              width: '100%',
              padding: '12px 16px',
              border: '1px solid #d1d5db',
              borderRadius: '6px',
              fontSize: '16px',
              backgroundColor: 'white'
            }}
            disabled={isSubmitting}
          >
            <option value="">Select {label}</option>
            {options.map(option => (
              <option key={option} value={option}>{option}</option>
            ))}
          </select>
        ) : (
          <input
            type={type}
            value={formData[field]}
            onChange={(e) => handleInputChange(field, e.target.value)}
            placeholder={placeholder}
            className={`w-full ${icon ? 'pl-10' : 'pl-4'} pr-4 py-3 border rounded-md focus:ring-2 focus:ring-orange-500 outline-none transition-colors ${
              errors[field] ? 'border-red-500' : 'border-gray-300'
            }`}
            style={{
              width: '100%',
              padding: '12px 16px',
              border: '1px solid #d1d5db',
              borderRadius: '6px',
              fontSize: '16px',
              backgroundColor: 'white'
            }}
            disabled={isSubmitting}
          />
        )}
      </div>
      {errors[field] && (
        <p className="text-red-500 text-sm mt-1" style={{ fontSize: '14px', color: '#ef4444', marginTop: '4px' }}>
          {errors[field]}
        </p>
      )}
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50 py-8" style={{ fontSize: '18px' }}>
      <div className="max-w-6xl mx-auto px-4">
        {/* Header - Exact match with Mimo Form.html */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-800 mb-2" style={{ fontSize: '32px', fontWeight: 'bold' }}>
            KPI Validation Form
          </h1>
          <p className="text-gray-600" style={{ fontSize: '18px' }}>
            Submit your validation request for processing
          </p>
        </div>

        {/* Success/Error Messages */}
        {submitResult && (
          <div className={`mb-6 p-4 rounded-lg flex items-center ${
            submitResult.success ? 'bg-green-50 text-green-800 border border-green-200' : 'bg-red-50 text-red-800 border border-red-200'
          }`}>
            {submitResult.success ? (
              <CheckCircle className="w-5 h-5 mr-2" />
            ) : (
              <AlertCircle className="w-5 h-5 mr-2" />
            )}
            <div>
              {submitResult.success ? (
                <>
                  <p className="font-semibold">Form submitted successfully!</p>
                  {submitResult.ticketId && (
                    <p className="text-sm">Ticket ID: {submitResult.ticketId}</p>
                  )}
                </>
              ) : (
                <p>{submitResult.error}</p>
              )}
            </div>
          </div>
        )}

        {/* Form Container - Exact match with Mimo Form.html */}
        <div className="form-container">
          <div className="w-full max-w-9xl bg-white rounded-lg p-8 mt-10" style={{
            boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
            borderRadius: '8px',
            padding: '32px'
          }}>
            <form onSubmit={handleSubmit} className="grid grid-cols-1 md:grid-cols-2 gap-6" style={{ gap: '24px' }}>
            {/* Service Provider */}
            <InputField
              label="Service Provider"
              field="serviceProvider"
              required
              icon={<User className="w-5 h-5" />}
            />

            {/* KPI */}
            <InputField
              label="KPI"
              field="kpi"
              required
              placeholder="Enter KPI name"
            />

            {/* Category */}
            <InputField
              label="Category"
              field="category"
              required
              options={['Products', 'Services', 'Analytics', 'Marketing']}
            />

            {/* Subcategory */}
            <InputField
              label="Subcategory"
              field="subcategory"
              placeholder="Enter subcategory"
            />

            {/* Row */}
            <InputField
              label="Row"
              field="row"
              placeholder="Enter row number"
            />

            {/* Validation Type */}
            <InputField
              label="Validation Type"
              field="validationType"
              options={['Field Validation', 'Data Validation', 'Process Validation', 'System Validation']}
            />

            {/* Personal Information */}
            <div className="md:col-span-2">
              <h3 className="text-lg font-semibold text-gray-800 mb-4 border-b pb-2">Personal Information</h3>
            </div>

            <InputField
              label="First Name"
              field="firstName"
              required
              icon={<User className="w-5 h-5" />}
              placeholder="Enter first name"
            />

            <InputField
              label="Last Name"
              field="lastName"
              required
              icon={<User className="w-5 h-5" />}
              placeholder="Enter last name"
            />

            <InputField
              label="Email"
              field="email"
              type="email"
              required
              icon={<Mail className="w-5 h-5" />}
              placeholder="Enter email address"
            />

            <InputField
              label="Phone"
              field="phone"
              type="tel"
              required
              icon={<Phone className="w-5 h-5" />}
              placeholder="Enter 10-digit phone number"
            />

            {/* Address Information */}
            <div className="md:col-span-2">
              <h3 className="text-lg font-semibold text-gray-800 mb-4 border-b pb-2">Address Information</h3>
            </div>

            <div className="md:col-span-2">
              <InputField
                label="Address"
                field="address"
                required
                icon={<MapPin className="w-5 h-5" />}
                placeholder="Enter full address"
              />
            </div>

            <InputField
              label="City"
              field="city"
              required
              placeholder="Enter city"
            />

            <InputField
              label="State"
              field="state"
              required
              options={['Delhi', 'West Bengal', 'Maharashtra', 'Karnataka', 'Tamil Nadu', 'Gujarat', 'Rajasthan', 'Uttar Pradesh']}
            />

            <InputField
              label="Pincode"
              field="pincode"
              required
              placeholder="Enter 6-digit pincode"
            />

            {/* Visit Information */}
            <div className="md:col-span-2">
              <h3 className="text-lg font-semibold text-gray-800 mb-4 border-b pb-2">Visit Information</h3>
            </div>

            <InputField
              label="Visit Date"
              field="visitDate"
              type="date"
              icon={<Calendar className="w-5 h-5" />}
            />

            <InputField
              label="Visit Time"
              field="visitTime"
              type="time"
            />

            {/* Comments */}
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Comments
              </label>
              <div className="relative">
                <FileText className="absolute left-3 top-3 text-gray-400 w-5 h-5" />
                <textarea
                  value={formData.comments}
                  onChange={(e) => handleInputChange('comments', e.target.value)}
                  placeholder="Enter any additional comments or notes"
                  rows={4}
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 outline-none transition-colors resize-vertical"
                  disabled={isSubmitting}
                />
              </div>
            </div>

            {/* Submit Button - Exact match with Mimo Form.html */}
            <div className="md:col-span-2 flex justify-center pt-6" style={{ paddingTop: '32px' }}>
              <button
                type="submit"
                disabled={isSubmitting}
                className="bg-orange-500 hover:bg-orange-600 text-white font-semibold py-3 px-8 rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                style={{
                  backgroundColor: '#ff6600',
                  color: 'white',
                  padding: '12px 32px',
                  borderRadius: '6px',
                  fontWeight: '600',
                  fontSize: '18px',
                  border: 'none',
                  cursor: isSubmitting ? 'not-allowed' : 'pointer',
                  opacity: isSubmitting ? 0.7 : 1,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}
              >
                {isSubmitting ? (
                  <>
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2" style={{
                      animation: 'spin 1s linear infinite',
                      borderRadius: '50%',
                      borderBottom: '2px solid white',
                      width: '20px',
                      height: '20px',
                      marginRight: '8px'
                    }}></div>
                    Submitting...
                  </>
                ) : (
                  <>
                    <Send className="w-5 h-5 mr-2" style={{ width: '20px', height: '20px', marginRight: '8px' }} />
                    Submit Form
                  </>
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};
