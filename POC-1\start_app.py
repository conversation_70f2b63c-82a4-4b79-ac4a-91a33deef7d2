#!/usr/bin/env python3
"""
Startup script for GeoIQ Analytics Dashboard
This script opens the login page first, then starts the Streamlit app.
"""

import os
import sys
import time
import webbrowser
import subprocess
import threading
from pathlib import Path

def open_login_page():
    """Open the login.html file in the default browser."""
    login_file_path = Path("login.html").absolute()
    
    if login_file_path.exists():
        try:
            # Convert to file:// URL for better browser compatibility
            login_url = f"file://{login_file_path.as_posix()}"
            
            print(f"🌐 Opening login page: {login_url}")
            webbrowser.open(login_url)
            return True
        except Exception as e:
            print(f"❌ Error opening login page: {e}")
            return False
    else:
        print(f"❌ Login file not found: {login_file_path}")
        return False

def find_project_directory():
    """Find the correct project directory containing the Streamlit app"""
    current_dir = os.getcwd()

    # Check if we're already in the right directory
    if os.path.exists(os.path.join(current_dir, 'pages', 'Interactive_Map.py')):
        return current_dir

    # Check if POC-1 subdirectory exists
    if os.path.exists(os.path.join(current_dir, 'POC-1', 'pages', 'Interactive_Map.py')):
        return os.path.join(current_dir, 'POC-1')

    # Look for POC-1 directory in current path
    if 'POC-1' in current_dir:
        path_parts = current_dir.split(os.sep)
        poc_index = -1
        for i, part in enumerate(path_parts):
            if 'POC-1' in part:
                poc_index = i
                break
        if poc_index >= 0:
            project_dir = os.sep.join(path_parts[:poc_index + 1])
            if os.path.exists(os.path.join(project_dir, 'pages', 'Interactive_Map.py')):
                return project_dir

    # Search upward (limited to 3 levels)
    search_dir = current_dir
    for _ in range(3):
        if os.path.exists(os.path.join(search_dir, 'pages', 'Interactive_Map.py')):
            return search_dir
        parent = os.path.dirname(search_dir)
        if parent == search_dir:  # Reached root
            break
        search_dir = parent

    return None

def start_streamlit_app():
    """Start the Streamlit application."""
    try:
        print("🚀 Starting Streamlit application...")

        # Find and change to the correct project directory
        project_dir = find_project_directory()
        if not project_dir:
            print("❌ Could not find the project directory!")
            return None

        print(f"📁 Project directory: {project_dir}")
        os.chdir(project_dir)
        
        # Start Streamlit
        cmd = [sys.executable, "-m", "streamlit", "run", "Stonesburry Mimo Analytics Dashboard.py", "--server.port=8501"]
        
        # On Windows, hide the console window for the subprocess
        if sys.platform.startswith('win'):
            startupinfo = subprocess.STARTUPINFO()
            startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
            startupinfo.wShowWindow = subprocess.SW_HIDE
            process = subprocess.Popen(cmd, startupinfo=startupinfo)
        else:
            process = subprocess.Popen(cmd)
        
        print("✅ Streamlit application started successfully!")
        print("📱 Dashboard will be available at: http://localhost:8501")
        print("🔐 Please complete the login process in your browser.")
        
        return process
    except Exception as e:
        print(f"❌ Error starting Streamlit: {e}")
        return None

def main():
    """Main function to orchestrate the startup process."""
    print("=" * 60)
    print("🎯 GeoIQ Analytics Dashboard - Startup")
    print("=" * 60)
    
    # Check if required files exist
    required_files = ["login.html", "Stonesburry Mimo Analytics Dashboard.py", "auth.py"]
    missing_files = []
    
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
    
    if missing_files:
        print("❌ Missing required files:")
        for file in missing_files:
            print(f"   - {file}")
        print("\nPlease ensure all required files are present and try again.")
        return
    
    print("✅ All required files found.")
    
    # Step 1: Open login page
    print("\n📋 Step 1: Opening login page...")
    if not open_login_page():
        print("❌ Failed to open login page. Please manually open login.html")
        return
    
    # Step 2: Wait a moment for the browser to load
    print("⏳ Waiting for browser to load...")
    time.sleep(3)
    
    # Step 3: Start Streamlit app
    print("\n📋 Step 2: Starting Streamlit application...")
    streamlit_process = start_streamlit_app()
    
    if streamlit_process is None:
        print("❌ Failed to start Streamlit application.")
        return
    
    print("\n" + "=" * 60)
    print("🎉 STARTUP COMPLETE!")
    print("=" * 60)
    print("📝 Instructions:")
    print("1. Complete the login process in your browser")
    print("2. Use credentials: <EMAIL> / user1234")
    print("3. After successful login, you'll be redirected to the dashboard")
    print("4. Press Ctrl+C to stop the application")
    print("=" * 60)
    
    try:
        # Keep the script running and monitor the Streamlit process
        while True:
            if streamlit_process.poll() is not None:
                print("\n⚠️  Streamlit process has stopped.")
                break
            time.sleep(5)
    except KeyboardInterrupt:
        print("\n🛑 Shutting down application...")
        try:
            streamlit_process.terminate()
            streamlit_process.wait(timeout=5)
        except:
            streamlit_process.kill()
        print("✅ Application stopped successfully.")

if __name__ == "__main__":
    main()
