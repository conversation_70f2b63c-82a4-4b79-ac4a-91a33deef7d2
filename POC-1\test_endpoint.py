#!/usr/bin/env python3
"""
Test script for Flask endpoints
"""

import requests
import json

def test_endpoint(url, description):
    """Test a single endpoint"""
    print(f"\n🧪 Testing: {description}")
    print(f"URL: {url}")
    
    try:
        response = requests.get(url, timeout=5)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"Response: {json.dumps(data, indent=2)}")
            except:
                print(f"Response (text): {response.text[:200]}...")
        else:
            print(f"Error Response: {response.text}")
            
    except requests.exceptions.Timeout:
        print("❌ Request timed out")
    except requests.exceptions.ConnectionError:
        print("❌ Connection error - server might not be running")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    base_url = "http://localhost:5000"
    
    # Test endpoints
    test_endpoint(f"{base_url}/api/health", "Health Check")
    test_endpoint(f"{base_url}/api/test/category/CeilingFans", "Test Category Endpoint")
    test_endpoint(f"{base_url}/api/category/image/CeilingFans", "Category Image Endpoint")
