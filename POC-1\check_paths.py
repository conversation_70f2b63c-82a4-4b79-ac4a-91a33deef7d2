#!/usr/bin/env python3
"""
Path and Connection Checker for POC-1 Project
This script checks all file paths, data files, and connections to ensure everything works after importing from another computer.
"""

import os
import sys
from pathlib import Path

def check_file_exists(file_path, description=""):
    """Check if a file exists and print status"""
    if os.path.exists(file_path):
        print(f"✅ {description}: {file_path}")
        return True
    else:
        print(f"❌ MISSING {description}: {file_path}")
        return False

def check_directory_exists(dir_path, description=""):
    """Check if a directory exists and print status"""
    if os.path.exists(dir_path) and os.path.isdir(dir_path):
        print(f"✅ {description}: {dir_path}")
        return True
    else:
        print(f"❌ MISSING {description}: {dir_path}")
        return False

def main():
    print("=" * 80)
    print("🔍 POC-1 PROJECT PATH AND CONNECTION CHECKER")
    print("=" * 80)
    
    # Get current working directory
    current_dir = os.getcwd()
    print(f"📁 Current working directory: {current_dir}")
    print()
    
    # Track missing files
    missing_files = []
    missing_dirs = []
    
    print("🗂️  CHECKING CORE DATA FILES:")
    print("-" * 40)
    
    # Core CSV files
    csv_files = [
        ("amaron_retailer_image.csv", "Amaron retailer data"),
        ("exide_retailer_image.csv", "Exide retailer data"),
        ("tata_green_retailer_image.csv", "Tata Green retailer data"),
        ("luminous_retailer_image.csv", "Luminous retailer data"),
        ("delhi_districts.geojson", "Delhi districts GeoJSON"),
        ("DataValues-Dashboard-CSV.csv", "Dashboard data CSV")
    ]
    
    for file_path, desc in csv_files:
        if not check_file_exists(file_path, desc):
            missing_files.append(file_path)
    
    print()
    print("📊 CHECKING EXCEL FILES:")
    print("-" * 40)
    
    # Excel files
    excel_files = [
        ("YusufSaraiAmaronCentre-FinalCSV.xlsx", "Main Excel file (root)"),
        ("Centres/Delhi/YusufSaraiAmaronCentre-FinalCSV.xlsx", "Delhi Excel file"),
        ("Centres/Delhi/YusufSaraiAmaronCentre-FinalCSV.xls", "Delhi XLS file"),
        ("Merged_Amaron_Population_SouthDelhi.xlsx", "Merged Amaron data"),
        ("fpt0705_New_Delhi-2001(Sheet1).csv", "New Delhi census data")
    ]
    
    for file_path, desc in excel_files:
        if not check_file_exists(file_path, desc):
            missing_files.append(file_path)
    
    print()
    print("🖼️  CHECKING IMAGE DIRECTORIES:")
    print("-" * 40)
    
    # Image directories
    image_dirs = [
        ("GeoIQimages", "Main GeoIQ images"),
        ("GeoIQimages/YusufSaraiGEOIQImages", "Delhi GeoIQ images"),
        ("GeoIQimages/South24ParganasGEOIQImages", "West Bengal GeoIQ images"),
        ("Items", "Main Items directory"),
        ("Items/YusufSaraiItemPrice", "Delhi item prices"),
        ("Items/South24ParganasItemPrices", "West Bengal item prices"),
        ("images", "General images"),
        ("interactive_images", "Interactive images"),
        ("interactive_images/Delhi", "Delhi interactive images"),
        ("interactive_images/West Bengal", "West Bengal interactive images"),
        ("logo", "Logo directory")
    ]
    
    for dir_path, desc in image_dirs:
        if not check_directory_exists(dir_path, desc):
            missing_dirs.append(dir_path)
    
    print()
    print("🌐 CHECKING WEB FILES:")
    print("-" * 40)
    
    # Web files
    web_files = [
        ("login.html", "Login page"),
        ("Mimo Form.html", "Mimo form"),
        ("detailed_analytics_form.html", "Analytics form"),
        ("test_map.html", "Test map"),
        ("delhi_dealer_district_map_with_images - Copy 1.html", "Delhi dealer map")
    ]
    
    for file_path, desc in web_files:
        if not check_file_exists(file_path, desc):
            missing_files.append(file_path)
    
    print()
    print("🐍 CHECKING PYTHON FILES:")
    print("-" * 40)
    
    # Python files
    python_files = [
        ("app.py", "Main app"),
        ("auth.py", "Authentication"),
        ("StonesburryMimoAnalyticsDashboard.py", "Main dashboard"),
        ("dashboard_amaron.py", "Amaron dashboard"),
        ("map5.py", "Map utilities"),
        ("map_utils.py", "Map utilities"),
        ("start_app.py", "Startup script"),
        ("pages/Interactive_Map.py", "Interactive map page"),
        ("pages/Mimo_Form.py", "Mimo form page")
    ]
    
    for file_path, desc in python_files:
        if not check_file_exists(file_path, desc):
            missing_files.append(file_path)
    
    print()
    print("🔧 CHECKING CONFIGURATION FILES:")
    print("-" * 40)
    
    # Config files
    config_files = [
        ("requirements.txt", "Python requirements"),
        ("start_app.bat", "Windows startup batch")
    ]
    
    for file_path, desc in config_files:
        if not check_file_exists(file_path, desc):
            missing_files.append(file_path)
    
    print()
    print("=" * 80)
    print("📋 SUMMARY REPORT")
    print("=" * 80)
    
    if not missing_files and not missing_dirs:
        print("🎉 ALL FILES AND DIRECTORIES FOUND!")
        print("✅ Your project should work correctly.")
    else:
        print("⚠️  ISSUES FOUND:")
        
        if missing_files:
            print(f"\n❌ Missing {len(missing_files)} files:")
            for file in missing_files:
                print(f"   - {file}")
        
        if missing_dirs:
            print(f"\n❌ Missing {len(missing_dirs)} directories:")
            for dir in missing_dirs:
                print(f"   - {dir}")
    
    print()
    print("🔍 ADDITIONAL CHECKS:")
    print("-" * 40)
    
    # Check if we can import required modules
    try:
        import streamlit
        print("✅ Streamlit is installed")
    except ImportError:
        print("❌ Streamlit is NOT installed - run: pip install streamlit")
    
    try:
        import pandas
        print("✅ Pandas is installed")
    except ImportError:
        print("❌ Pandas is NOT installed - run: pip install pandas")
    
    try:
        import folium
        print("✅ Folium is installed")
    except ImportError:
        print("❌ Folium is NOT installed - run: pip install folium")
    
    try:
        import PIL
        print("✅ Pillow (PIL) is installed")
    except ImportError:
        print("❌ Pillow is NOT installed - run: pip install pillow")
    
    try:
        import geopy
        print("✅ Geopy is installed")
    except ImportError:
        print("❌ Geopy is NOT installed - run: pip install geopy")
    
    try:
        import plotly
        print("✅ Plotly is installed")
    except ImportError:
        print("❌ Plotly is NOT installed - run: pip install plotly")
    
    try:
        import openai
        print("✅ OpenAI is installed")
    except ImportError:
        print("❌ OpenAI is NOT installed - run: pip install openai")

if __name__ == "__main__":
    main()
