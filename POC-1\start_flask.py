#!/usr/bin/env python3
"""
Startup script for Flask backend
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def check_requirements():
    """Check if Flask requirements are installed"""
    try:
        import flask
        import flask_cors
        import pandas
        import folium
        import geopy
        from PIL import Image
        print("✅ All Flask requirements are installed")
        return True
    except ImportError as e:
        print(f"❌ Missing requirement: {e}")
        return False

def install_requirements():
    """Install Flask requirements"""
    print("📦 Installing Flask backend requirements...")
    try:
        subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "flask_requirements.txt"
        ], check=True)
        print("✅ Flask requirements installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install requirements: {e}")
        return False

def check_data_files():
    """Check if required data files exist"""
    required_files = [
        "amaron_retailer_image.csv",
        "exide_retailer_image.csv", 
        "tata_green_retailer_image.csv",
        "luminous_retailer_image.csv",
        "DataValues-Dashboard-CSV.csv"
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("⚠️  Warning: Some data files are missing:")
        for file in missing_files:
            print(f"   - {file}")
        print("   The API will still work but some features may be limited.")
    else:
        print("✅ All required data files found")
    
    return len(missing_files) == 0

def start_flask_server():
    """Start the Flask development server"""
    print("🚀 Starting Flask backend server...")
    print("📍 Server will be available at: http://localhost:5000")
    print("📋 API endpoints:")
    print("   - Authentication: /api/auth/*")
    print("   - Maps: /api/map/*")
    print("   - Images: /api/images/*")
    print("   - Forms: /api/form/*")
    print("   - Data: /api/data/*")
    print("   - Health: /api/health")
    print("\n" + "="*50)
    print("Press Ctrl+C to stop the server")
    print("="*50 + "\n")
    
    try:
        # Set environment variables
        os.environ['FLASK_ENV'] = 'development'
        os.environ['FLASK_DEBUG'] = '1'
        
        # Start Flask app
        subprocess.run([sys.executable, "flask_backend.py"])
    except KeyboardInterrupt:
        print("\n🛑 Flask server stopped by user")
    except Exception as e:
        print(f"❌ Error starting Flask server: {e}")

def main():
    """Main startup function"""
    print("🔧 POC-1 Flask Backend Startup")
    print("=" * 40)
    
    # Check if we're in the right directory
    if not os.path.exists("flask_backend.py"):
        print("❌ flask_backend.py not found!")
        print("   Please run this script from the POC-1 directory")
        sys.exit(1)
    
    # Check and install requirements
    if not check_requirements():
        print("📦 Installing missing requirements...")
        if not install_requirements():
            print("❌ Failed to install requirements. Please install manually:")
            print("   pip install -r flask_requirements.txt")
            sys.exit(1)
        
        # Check again after installation
        if not check_requirements():
            print("❌ Requirements still missing after installation")
            sys.exit(1)
    
    # Check data files
    check_data_files()
    
    # Create necessary directories
    os.makedirs("submitted_forms", exist_ok=True)
    print("✅ Created submitted_forms directory")
    
    print("\n🎯 All checks passed! Starting Flask server...")
    time.sleep(1)
    
    # Start the server
    start_flask_server()

if __name__ == "__main__":
    main()
