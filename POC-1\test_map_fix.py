#!/usr/bin/env python3
"""Test script to verify Delhi map generation fixes"""

import pandas as pd
import folium
import os

def clean_coordinates(row):
    """Clean and separate concatenated coordinates"""
    lat = row['latitude']
    lon = row['longitude']
    
    # Handle concatenated coordinates (like "28.56913577.242321")
    if pd.isna(lon) and isinstance(lat, str) and len(lat) > 10:
        # Try to split concatenated coordinates
        lat_str = str(lat)
        if '77.' in lat_str and '28.' in lat_str:
            # Find the split point (usually where 77 starts)
            split_idx = lat_str.find('77.')
            if split_idx > 0:
                clean_lat = lat_str[:split_idx]
                clean_lon = lat_str[split_idx:]
                try:
                    return pd.Series([float(clean_lat), float(clean_lon)])
                except:
                    pass
    
    # Try to convert normally
    try:
        clean_lat = float(lat) if not pd.isna(lat) else None
        clean_lon = float(lon) if not pd.isna(lon) else None
        return pd.Series([clean_lat, clean_lon])
    except:
        return pd.Series([None, None])

def test_delhi_map():
    """Test Delhi map generation"""
    try:
        # Load data
        df = pd.read_excel('Centres/Delhi/YusufSaraiAmaronCentre-FinalCSV.xlsx')
        print(f'✅ Loaded {len(df)} rows from Excel')
        
        # Clean coordinates
        df[['latitude', 'longitude']] = df.apply(clean_coordinates, axis=1)
        valid_df = df.dropna(subset=['latitude', 'longitude'])
        print(f'✅ Valid coordinates: {len(valid_df)} out of {len(df)}')
        
        # Filter for Delhi region
        delhi_bounds = {
            'lat_min': 28.4, 'lat_max': 28.8,
            'lon_min': 76.8, 'lon_max': 77.5
        }
        
        valid_df = valid_df[
            (valid_df['latitude'] >= delhi_bounds['lat_min']) &
            (valid_df['latitude'] <= delhi_bounds['lat_max']) &
            (valid_df['longitude'] >= delhi_bounds['lon_min']) &
            (valid_df['longitude'] <= delhi_bounds['lon_max'])
        ]
        
        print(f'✅ Delhi region coordinates: {len(valid_df)}')
        
        if len(valid_df) == 0:
            print('❌ No valid Delhi coordinates found')
            return False
        
        # Create map
        center_lat = valid_df['latitude'].mean()
        center_lon = valid_df['longitude'].mean()
        print(f'✅ Map center: {center_lat:.6f}, {center_lon:.6f}')
        
        m = folium.Map(location=[center_lat, center_lon], zoom_start=14)
        
        # Add markers
        markers_added = 0
        for idx, row in valid_df.iterrows():
            try:
                folium.Marker(
                    [row['latitude'], row['longitude']],
                    popup=f"{row['Brand']}: {row['Name']}",
                    icon=folium.Icon(color='red', icon='info-sign')
                ).add_to(m)
                markers_added += 1
            except Exception as e:
                print(f'⚠️ Failed to add marker for row {idx}: {e}')
        
        print(f'✅ Added {markers_added} markers to map')
        print('✅ Map generation successful!')
        return True
        
    except Exception as e:
        print(f'❌ Error: {e}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("Testing Delhi map generation...")
    success = test_delhi_map()
    if success:
        print("\n🎉 All tests passed! The map should work now.")
    else:
        print("\n💥 Tests failed. Check the errors above.")
