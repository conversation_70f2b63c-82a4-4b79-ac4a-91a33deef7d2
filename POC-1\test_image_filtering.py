#!/usr/bin/env python3
"""
Test script to verify the image filtering logic for Interactive_Map.py
"""

import os
import glob
import sys

def test_get_interactive_images(search_query="", selected_state="", show_all_competitors=False):
    """Test version of get_interactive_images function"""
    try:
        images = []
        base_path = "interactive_images"

        if not os.path.exists(base_path):
            print(f"❌ Base path {base_path} does not exist")
            return []

        print(f"🔍 Testing with: state='{selected_state}', search='{search_query}', show_all={show_all_competitors}")

        # Determine which state folder to search based on selected_state
        search_folders = []
        
        if selected_state == "Delhi(NCT)":
            # For Delhi, look specifically in the Delhi folder
            delhi_folder = os.path.join(base_path, "Delhi")
            if os.path.exists(delhi_folder) and os.path.isdir(delhi_folder):
                print(f"✅ Found Delhi folder: {delhi_folder}")
                # Get all subfolders within Delhi folder
                for subfolder in os.listdir(delhi_folder):
                    subfolder_path = os.path.join(delhi_folder, subfolder)
                    if os.path.isdir(subfolder_path):
                        search_folders.append(subfolder_path)
                        print(f"  📁 Added Delhi subfolder: {subfolder_path}")
        elif selected_state == "West Bengal":
            # For West Bengal, look specifically in the West Bengal folder
            wb_folder = os.path.join(base_path, "West Bengal")
            if os.path.exists(wb_folder) and os.path.isdir(wb_folder):
                print(f"✅ Found West Bengal folder: {wb_folder}")
                # Get all subfolders within West Bengal folder
                for subfolder in os.listdir(wb_folder):
                    subfolder_path = os.path.join(wb_folder, subfolder)
                    if os.path.isdir(subfolder_path):
                        search_folders.append(subfolder_path)
                        print(f"  📁 Added West Bengal subfolder: {subfolder_path}")
        else:
            # If no specific state selected or unknown state, search all folders
            print("🔍 No specific state selected, searching all folders")
            for state_folder in os.listdir(base_path):
                state_folder_path = os.path.join(base_path, state_folder)
                if os.path.isdir(state_folder_path):
                    # Get all subfolders within each state folder
                    for subfolder in os.listdir(state_folder_path):
                        subfolder_path = os.path.join(state_folder_path, subfolder)
                        if os.path.isdir(subfolder_path):
                            search_folders.append(subfolder_path)
                            print(f"  📁 Added general subfolder: {subfolder_path}")

        # If no specific folders found, fallback to all available folders
        if not search_folders:
            print("⚠️ No specific folders found, using fallback")
            for state_folder in os.listdir(base_path):
                state_folder_path = os.path.join(base_path, state_folder)
                if os.path.isdir(state_folder_path):
                    search_folders.append(state_folder_path)
                    print(f"  📁 Added fallback folder: {state_folder_path}")

        print(f"📂 Total search folders: {len(search_folders)}")

        # Collect images from relevant folders
        for folder_path in search_folders:
            if os.path.exists(folder_path):
                print(f"🔍 Searching in: {folder_path}")
                # Get all image files
                image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.gif', '*.bmp']
                folder_image_count = 0
                for ext in image_extensions:
                    pattern = os.path.join(folder_path, ext)
                    folder_images = glob.glob(pattern)
                    folder_image_count += len(folder_images)

                    for img_path in folder_images:
                        # Extract brand from folder name or filename
                        folder_name = os.path.basename(folder_path).lower()
                        filename = os.path.basename(img_path).lower()

                        # Determine brand
                        brand = ""
                        if "amaron" in folder_name or "amaron" in filename:
                            brand = "amaron"
                        elif "exide" in folder_name or "exide" in filename:
                            brand = "exide"
                        elif "tata" in folder_name or "tata" in filename:
                            brand = "tata"

                        # Apply search filter (only if not showing all competitors)
                        if search_query and not show_all_competitors:
                            search_lower = search_query.lower().strip()
                            if search_lower and search_lower not in brand and search_lower not in filename:
                                continue

                        images.append({
                            'path': img_path,
                            'filename': os.path.basename(img_path),
                            'brand': brand,
                            'folder': os.path.basename(folder_path),
                            'state': os.path.basename(os.path.dirname(folder_path)) if os.path.dirname(folder_path) != base_path else 'Unknown'
                        })

                print(f"  📸 Found {folder_image_count} images in {os.path.basename(folder_path)}")

        print(f"✅ Total images found: {len(images)}")
        return images

    except Exception as e:
        print(f"❌ Error loading images: {str(e)}")
        return []

def main():
    """Test the image filtering functionality"""
    print("🧪 Testing Interactive Image Gallery Filtering")
    print("=" * 50)
    
    # Test cases
    test_cases = [
        ("Delhi(NCT)", "", False, "Delhi - All brands"),
        ("West Bengal", "", False, "West Bengal - All brands"),
        ("Delhi(NCT)", "amaron", False, "Delhi - Amaron only"),
        ("West Bengal", "exide", False, "West Bengal - Exide only"),
        ("", "", True, "All states - All competitors"),
    ]
    
    for state, search, show_all, description in test_cases:
        print(f"\n🔬 Test: {description}")
        print("-" * 30)
        images = test_get_interactive_images(search, state, show_all)
        
        if images:
            # Group by state and brand
            state_brand_count = {}
            for img in images:
                img_state = img.get('state', 'Unknown')
                img_brand = img.get('brand', 'Unknown')
                key = f"{img_state} - {img_brand}"
                state_brand_count[key] = state_brand_count.get(key, 0) + 1
            
            print("📊 Results:")
            for key, count in sorted(state_brand_count.items()):
                print(f"  {key}: {count} images")
        else:
            print("📊 No images found")
        
        print()

if __name__ == "__main__":
    main()
