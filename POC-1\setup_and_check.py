#!/usr/bin/env python3
"""
Setup and Check <PERSON>ript for POC-1 Project
This script installs dependencies and checks all paths and connections.
"""

import subprocess
import sys
import os
from pathlib import Path

def install_package(package):
    """Install a package using pip"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        return True
    except subprocess.CalledProcessError:
        return False

def check_and_install_dependencies():
    """Check and install all required dependencies"""
    print("🔧 INSTALLING DEPENDENCIES")
    print("=" * 50)
    
    # Required packages
    packages = [
        "streamlit>=1.45.1",
        "pandas>=1.5.0", 
        "pillow>=9.0.0",
        "folium>=0.19.0",
        "geopy>=2.4.0",
        "openai",
        "plotly"
    ]
    
    for package in packages:
        print(f"Installing {package}...")
        if install_package(package):
            print(f"✅ {package} installed successfully")
        else:
            print(f"❌ Failed to install {package}")
    
    print()

def check_file_exists(file_path, description=""):
    """Check if a file exists and print status"""
    if os.path.exists(file_path):
        print(f"✅ {description}: {file_path}")
        return True
    else:
        print(f"❌ MISSING {description}: {file_path}")
        return False

def check_critical_files():
    """Check for critical files that the application needs"""
    print("🗂️  CHECKING CRITICAL FILES")
    print("=" * 50)
    
    critical_files = [
        ("requirements.txt", "Requirements file"),
        ("StonesburryMimoAnalyticsDashboard.py", "Main dashboard"),
        ("auth.py", "Authentication module"),
        ("login.html", "Login page"),
        ("amaron_retailer_image.csv", "Amaron data"),
        ("exide_retailer_image.csv", "Exide data"),
        ("tata_green_retailer_image.csv", "Tata data"),
        ("luminous_retailer_image.csv", "Luminous data"),
        ("YusufSaraiAmaronCentre-FinalCSV.xlsx", "Main Excel data"),
        ("DataValues-Dashboard-CSV.csv", "Dashboard CSV data")
    ]
    
    missing_files = []
    for file_path, desc in critical_files:
        if not check_file_exists(file_path, desc):
            missing_files.append(file_path)
    
    print()
    return missing_files

def check_directories():
    """Check for required directories"""
    print("📁 CHECKING DIRECTORIES")
    print("=" * 50)
    
    required_dirs = [
        ("GeoIQimages", "GeoIQ images"),
        ("Items", "Item images"),
        ("images", "General images"),
        ("logo", "Logo files"),
        ("pages", "Streamlit pages"),
        ("Centres", "Centre data"),
        ("interactive_images", "Interactive images")
    ]
    
    missing_dirs = []
    for dir_path, desc in required_dirs:
        if os.path.exists(dir_path) and os.path.isdir(dir_path):
            print(f"✅ {desc}: {dir_path}")
        else:
            print(f"❌ MISSING {desc}: {dir_path}")
            missing_dirs.append(dir_path)
    
    print()
    return missing_dirs

def test_imports():
    """Test if all required modules can be imported"""
    print("🐍 TESTING PYTHON IMPORTS")
    print("=" * 50)
    
    modules = [
        ("streamlit", "Streamlit"),
        ("pandas", "Pandas"),
        ("PIL", "Pillow"),
        ("folium", "Folium"),
        ("geopy", "Geopy"),
        ("plotly", "Plotly"),
        ("openai", "OpenAI")
    ]
    
    failed_imports = []
    for module, name in modules:
        try:
            __import__(module)
            print(f"✅ {name} imported successfully")
        except ImportError:
            print(f"❌ {name} import failed")
            failed_imports.append(name)
    
    print()
    return failed_imports

def create_missing_data_file():
    """Create a sample data file if the original is missing"""
    if not os.path.exists("fpt0705_New_Delhi-2001(Sheet1).csv"):
        print("📝 Creating sample data file...")
        
        # Create a simple CSV with the expected columns
        import pandas as pd
        
        sample_data = {
            'Total - Persons': [100000, 150000, 200000],
            'Total - Males': [52000, 78000, 104000],
            'Total - Females': [48000, 72000, 96000],
            'Scheduled Caste - Persons': [15000, 22500, 30000],
            'Scheduled Tribe - Persons': [2000, 3000, 4000]
        }
        
        df = pd.DataFrame(sample_data)
        df.to_csv("fpt0705_New_Delhi-2001(Sheet1).csv", index=False)
        print("✅ Sample data file created")

def main():
    """Main function"""
    print("🚀 POC-1 PROJECT SETUP AND CHECKER")
    print("=" * 60)
    print(f"📁 Working directory: {os.getcwd()}")
    print()
    
    # Step 1: Install dependencies
    check_and_install_dependencies()
    
    # Step 2: Test imports
    failed_imports = test_imports()
    
    # Step 3: Check files
    missing_files = check_critical_files()
    
    # Step 4: Check directories
    missing_dirs = check_directories()
    
    # Step 5: Create missing sample data
    create_missing_data_file()
    
    # Summary
    print("📋 SETUP SUMMARY")
    print("=" * 50)
    
    if not failed_imports and not missing_files and not missing_dirs:
        print("🎉 SETUP COMPLETE! Everything looks good.")
        print("✅ You can now run the applications:")
        print("   streamlit run StonesburryMimoAnalyticsDashboard.py")
        print("   streamlit run app.py")
    else:
        print("⚠️  ISSUES FOUND:")
        
        if failed_imports:
            print(f"\n❌ Failed imports: {', '.join(failed_imports)}")
            print("   Try: pip install -r requirements.txt")
        
        if missing_files:
            print(f"\n❌ Missing files ({len(missing_files)}):")
            for file in missing_files[:5]:  # Show first 5
                print(f"   - {file}")
            if len(missing_files) > 5:
                print(f"   ... and {len(missing_files) - 5} more")
        
        if missing_dirs:
            print(f"\n❌ Missing directories ({len(missing_dirs)}):")
            for dir in missing_dirs:
                print(f"   - {dir}")
    
    print("\n" + "=" * 60)

if __name__ == "__main__":
    main()
