// API service for communicating with Flask backend

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';

interface ApiResponse<T> {
  success?: boolean;
  data?: T;
  error?: string;
  message?: string;
}

class ApiService {
  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${API_BASE_URL}${endpoint}`;
    
    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      credentials: 'include', // Include cookies for session management
      ...options,
    };

    try {
      const response = await fetch(url, config);
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error(`API request failed: ${endpoint}`, error);
      throw error;
    }
  }

  // Authentication endpoints
  async login(email: string, password: string) {
    return this.request<{
      success: boolean;
      message: string;
      user: { email: string };
      session_id: string;
    }>('/auth/login', {
      method: 'POST',
      body: JSON.stringify({ email, password }),
    });
  }

  async logout() {
    return this.request<{ success: boolean; message: string }>('/auth/logout', {
      method: 'POST',
    });
  }

  async checkAuth() {
    return this.request<{
      authenticated: boolean;
      user?: { email: string };
      login_time?: string;
    }>('/auth/check');
  }

  // Map endpoints
  async getDealers() {
    return this.request<{
      dealers: Array<{
        name: string;
        pin: string;
        brand: string;
        color: string;
        lat: number;
        lon: number;
        image?: string;
        address?: string;
      }>;
    }>('/map/dealers');
  }

  async generateMap(options: {
    center_lat?: number;
    center_lon?: number;
    zoom?: number;
  }) {
    return this.request<{ map_html: string }>('/map/generate', {
      method: 'POST',
      body: JSON.stringify(options),
    });
  }

  // Image gallery endpoints
  async getImageGallery(params: {
    state?: string;
    brand?: string;
    show_competitors?: boolean;
  }) {
    const searchParams = new URLSearchParams();
    if (params.state) searchParams.append('state', params.state);
    if (params.brand) searchParams.append('brand', params.brand);
    if (params.show_competitors !== undefined) {
      searchParams.append('show_competitors', params.show_competitors.toString());
    }

    return this.request<{
      images: Array<{
        name: string;
        folder: string;
        brand: string;
        base64: string;
        path: string;
        state: string;
        timestamp: string;
      }>;
      total: number;
    }>(`/images/gallery?${searchParams}`);
  }

  // Form endpoints
  async submitMimoForm(formData: any) {
    return this.request<{
      success: boolean;
      ticket_id: string;
      message: string;
    }>('/form/submit', {
      method: 'POST',
      body: JSON.stringify(formData),
    });
  }

  async getFormTickets() {
    return this.request<{
      tickets: Array<{
        ticket_id: string;
        submitted_at: string;
        user_email: string;
      }>;
    }>('/form/tickets');
  }

  // Data endpoints
  async getCsvData(filename?: string) {
    const params = filename ? `?file=${encodeURIComponent(filename)}` : '';
    return this.request<{
      data: any[];
      columns: string[];
      total_rows: number;
    }>(`/data/csv${params}`);
  }

  // Health check
  async healthCheck() {
    return this.request<{
      status: string;
      timestamp: string;
      version: string;
    }>('/health');
  }
}

export const apiService = new ApiService();
export default apiService;
