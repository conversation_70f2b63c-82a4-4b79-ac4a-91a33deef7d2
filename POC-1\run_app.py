#!/usr/bin/env python3
"""
Startup script to ensure the Streamlit app runs from the correct directory
This script automatically detects the correct project directory and starts the app
"""

import os
import sys
import subprocess

def find_project_directory():
    """Find the correct project directory containing the Streamlit app"""
    current_dir = os.getcwd()
    
    # Check if we're already in the right directory
    if os.path.exists(os.path.join(current_dir, 'pages', 'Interactive_Map.py')):
        return current_dir
    
    # Check if POC-1 subdirectory exists
    if os.path.exists(os.path.join(current_dir, 'POC-1', 'pages', 'Interactive_Map.py')):
        return os.path.join(current_dir, 'POC-1')
    
    # Look for POC-1 directory in current path
    if 'POC-1' in current_dir:
        path_parts = current_dir.split(os.sep)
        poc_index = -1
        for i, part in enumerate(path_parts):
            if 'POC-1' in part:
                poc_index = i
                break
        if poc_index >= 0:
            project_dir = os.sep.join(path_parts[:poc_index + 1])
            if os.path.exists(os.path.join(project_dir, 'pages', 'Interactive_Map.py')):
                return project_dir
    
    # Search upward (limited to 3 levels)
    search_dir = current_dir
    for _ in range(3):
        if os.path.exists(os.path.join(search_dir, 'pages', 'Interactive_Map.py')):
            return search_dir
        parent = os.path.dirname(search_dir)
        if parent == search_dir:  # Reached root
            break
        search_dir = parent
    
    return None

def main():
    """Main function to start the Streamlit app"""
    print("🚀 Starting Streamlit app...")
    
    # Find the correct project directory
    project_dir = find_project_directory()
    
    if not project_dir:
        print("❌ Could not find the project directory!")
        print("Please make sure you're running this script from within the POC-1 project folder.")
        sys.exit(1)
    
    print(f"📁 Project directory: {project_dir}")
    
    # Change to the project directory
    os.chdir(project_dir)
    print(f"📂 Changed working directory to: {os.getcwd()}")
    
    # Verify required files exist
    required_files = [
        'pages/Interactive_Map.py',
        'Centres/Delhi/YusufSaraiAmaronCentre-FinalCSV.xlsx',
        'Centres/West bengal/South24ParganasAmaronCenter-CSV.csv'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print("❌ Missing required files:")
        for file_path in missing_files:
            print(f"  - {file_path}")
        print("\nPlease ensure all required files are present before running the app.")
        sys.exit(1)
    
    print("✅ All required files found!")
    
    # Start the Streamlit app
    try:
        print("🌐 Starting Streamlit server...")
        print("📱 The app will open in your default browser")
        print("🔗 If it doesn't open automatically, go to: http://localhost:8501")
        print("\n" + "="*50)
        
        # Run streamlit with the main dashboard file
        main_file = "StonesburryMimoAnalyticsDashboard.py"
        if os.path.exists(main_file):
            subprocess.run([sys.executable, "-m", "streamlit", "run", main_file])
        else:
            print(f"❌ Main file {main_file} not found!")
            print("Available Python files:")
            for file in os.listdir('.'):
                if file.endswith('.py'):
                    print(f"  - {file}")
            
    except KeyboardInterrupt:
        print("\n🛑 App stopped by user")
    except Exception as e:
        print(f"❌ Error starting the app: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
