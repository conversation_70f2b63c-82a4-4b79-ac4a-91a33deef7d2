#!/usr/bin/env python3
"""
Test script for image gallery functionality
"""

import os

def test_image_gallery():
    """Test the image gallery logic"""
    print("Testing image gallery functionality...")
    
    # Test the path structure
    base_path = "interactive_images/Delhi"
    print(f"Base path exists: {os.path.exists(base_path)}")
    
    if not os.path.exists(base_path):
        print("ERROR: Base path does not exist!")
        return
    
    # Test brand filtering
    brand_filter = "Amaron"
    images_found = []
    
    for root, dirs, files in os.walk(base_path):
        folder_name = os.path.basename(root)
        print(f"\nFolder: {folder_name}")
        
        # Test brand filtering logic
        if brand_filter and brand_filter != "All Brands":
            brand_in_folder = brand_filter.lower() in folder_name.lower()
            print(f"  Brand '{brand_filter}' in folder '{folder_name}': {brand_in_folder}")
            
            if not brand_in_folder:
                print(f"  Skipping folder (brand filter)")
                continue
        
        # Count images
        image_files = [f for f in files if f.lower().endswith(('.jpg', '.jpeg', '.png', '.gif', '.bmp'))]
        print(f"  Images found: {len(image_files)}")
        
        if len(image_files) > 0:
            print(f"  Sample images: {image_files[:3]}")
            
            # Extract brand name from folder
            brand_name = folder_name
            if '-' in folder_name:
                brand_name = folder_name.split('-')[-1]
            print(f"  Extracted brand: {brand_name}")
            
            images_found.extend(image_files)
    
    print(f"\nTotal images found for brand '{brand_filter}': {len(images_found)}")
    return len(images_found) > 0

if __name__ == "__main__":
    success = test_image_gallery()
    if success:
        print("\n✅ Image gallery test PASSED")
    else:
        print("\n❌ Image gallery test FAILED")
