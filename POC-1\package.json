{"name": "poc-1-integrated-dashboard", "private": true, "version": "1.0.0", "description": "Integrated POC-1 Dashboard with Flask Backend and React Frontend", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint .", "preview": "vite preview", "start-backend": "python start_flask.py", "start-frontend": "npm run dev", "start": "concurrently \"npm run start-backend\" \"npm run start-frontend\"", "install-python": "pip install -r flask_requirements.txt", "setup": "npm install && npm run install-python"}, "dependencies": {"html2canvas": "^1.4.1", "jspdf": "^2.5.1", "leaflet": "^1.9.4", "lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-leaflet": "^4.2.1", "recharts": "^2.8.0"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/leaflet": "^1.9.19", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "concurrently": "^8.2.2", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}, "engines": {"node": ">=16.0.0", "python": ">=3.8.0"}}