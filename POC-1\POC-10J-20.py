import streamlit as st
import os
from PIL import Image
import base64
from io import BytesIO
from pathlib import Path  # ✅ Fix for NameError


# --- App config ---
st.set_page_config(layout="wide", page_title="GeoIQ Image Viewer")

# Add logo at the top-left corner
def add_logo():
    """Add logo to the top-left corner of the page"""
    try:
        # Load and encode the logo
        import os
        import base64
        from io import BytesIO
        from PIL import Image

        # Load the new logos
        stonesbury_path = "logo/Stonesbury-logo.png"
        adobe_path = "logo/Adobe Express - file.png"

        logos_html = ""
        if os.path.exists(stonesbury_path):
            stonesbury_img = Image.open(stonesbury_path)
            buffered = BytesIO()
            stonesbury_img.save(buffered, format="PNG")
            stonesbury_base64 = base64.b64encode(buffered.getvalue()).decode()
            logos_html += f'<img src="data:image/png;base64,{stonesbury_base64}" alt="Stonesbury Logo" style="height: 45px; width: auto; margin-right: 15px;">'

        if os.path.exists(adobe_path):
            adobe_img = Image.open(adobe_path)
            buffered = BytesIO()
            adobe_img.save(buffered, format="PNG")
            adobe_base64 = base64.b64encode(buffered.getvalue()).decode()
            logos_html += f'<img src="data:image/png;base64,{adobe_base64}" alt="Adobe Express Logo" style="height: 45px; width: auto;">'

        if logos_html:
            # Update the HTML template to display both logos
            updated_logo_html = f"""
            <div style="position: fixed; top: 10px; left: 10px; z-index: 999; background: white; padding: 5px; border-radius: 5px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                {logos_html}
            </div>
            """
            st.markdown(updated_logo_html, unsafe_allow_html=True)
        else:
            # Fallback if logo file not found
            st.markdown("""
            <div style="position: fixed; top: 10px; left: 10px; z-index: 999; background: white; padding: 5px; border-radius: 5px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                <div style="height: 50px; width: 100px; background: #ff6600; color: white; display: flex; align-items: center; justify-content: center; font-weight: bold; border-radius: 3px;">
                    GeoIQ
                </div>
            </div>
            """, unsafe_allow_html=True)
    except Exception:
        # Silent fallback - don't disrupt the app if logo fails
        pass

# Add logo to the page
add_logo()

# --- Convert image to base64 for rendering with enhanced quality ---
def image_to_base64(img, quality=95, optimize=True):
    """
    Convert PIL image to base64 with enhanced quality settings
    """
    buffered = BytesIO()

    # Ensure image is in RGB mode for better quality
    if img.mode in ('RGBA', 'LA', 'P'):
        # Convert to RGB with white background for transparency
        background = Image.new('RGB', img.size, (255, 255, 255))
        if img.mode == 'P':
            img = img.convert('RGBA')
        background.paste(img, mask=img.split()[-1] if img.mode in ('RGBA', 'LA') else None)
        img = background
    elif img.mode != 'RGB':
        img = img.convert('RGB')

    # Save with high quality settings
    img.save(buffered, format="PNG", optimize=optimize, quality=quality)
    return base64.b64encode(buffered.getvalue()).decode()

# --- Custom CSS ---
st.markdown("""
    <style>
        html, body, [data-testid="stApp"] {
            background-color: white !important;
            color: #333 !important;
        }
        .stButton > button {
            background-color: #f8f9fa !important;
            color: #333 !important;
            border: 1px solid #ff6600 !important;
            padding: 8px 10px !important;
            font-size: 14px !important;
            width: 100% !important;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        h3, label, .stSelectbox label {
            color: orange !important;
        }
        .zoom-container {
            height: 800px;
            overflow: auto;
            border: 1px solid #555;
            padding: 5px 10px;
            background-color: #f8f9fa;
            margin-top: -30px;
            margin-left: -40px;
        }
        .zoom-controls {
            margin-bottom: 10px;
            position: relative;
            z-index: 10;
        }
    </style>
""", unsafe_allow_html=True)

# --- Session state ---
if "selected_button" not in st.session_state:
    st.session_state.selected_button = None
if "zoom_level" not in st.session_state:
    st.session_state.zoom_level = 120

# --- Button mappings ---
button_names = [
    "Live Map", "HH 5L+", "HH 10L+", "HH 20L+",
    "Trade Area", "Total FF", "Branded FF", "Hourly FF",
    "Daily FF", "Weekday Weekend FF", "Category Mix", "Shoppers From"
]
image_files = [
    None, "YSHH5LA-1", "YSHH10LA-2", "YSHH20LA-3",
    "YSTA-4", "YSTFF-5", "YSBFF-6", "YSHFF-7",
    "YSDFF-8", "YSWWFF-9", "YSCM-10", "YSWSCF-11"
]

# --- Sub-category image mappings ---
sub_category_mapping = {
    "Exhaust Fans": "ExhaustFans",
    "Ceiling Fans": "CeilingFans",
    "Room Lights": "RoomLights",
    "Light Holders": "LightHolders",
    "Desk Lights": "DeskLights",
    "Solar Lights": "SolarLights",
    "Food Processor": "FoodProcessor",
    "Home Tools": "HomeTools",
    "Electricians": "Electricians"
}

# --- Sidebar filters and buttons ---
with st.sidebar:
    st.markdown("## 🧭 Filters")
    st.selectbox("State", ["Delhi"], key="state")
    st.selectbox("Geography", ["South Central"], key="geography")
    st.selectbox("Locality", ["Yusuf Sarai"], key="locality")

    category = st.selectbox("Category", ["Electronics & H. Appliances", "Services"], key="category")
    subcategory_options = {
        "Electronics & H. Appliances": ["Select", "Exhaust Fans", "Ceiling Fans", "Room Lights", "Light Holders",
                                        "Desk Lights", "Solar Lights", "Food Processor", "Home Tools"],
        "Services": ["Electricians"]
    }
    subcategories = subcategory_options.get(category, [])
    st.selectbox("Sub Category", subcategories, key="sub_category")

    st.markdown("## 🖼️ Select View")
    for i in range(0, len(button_names), 2):
        cols = st.columns(2)
        for j in range(2):
            if i + j < len(button_names):
                if cols[j].button(button_names[i + j]):
                    st.session_state.selected_button = button_names[i + j]

    st.markdown("---")
    if st.button("📊 View Data"):
        js = "window.open('http://localhost:8501/1_View_Data')"
        st.components.v1.html(f"<script>{js}</script>", height=0)

# --- Zoom Buttons ---
def render_zoom_controls():
    col1, col2, col3 = st.columns([1, 1, 1])
    with col1:
        if st.button("➖ Zoom Out"):
            if st.session_state.zoom_level > 50:
                st.session_state.zoom_level -= 10
    with col2:
        if st.button("🔄 Reset Zoom"):
            st.session_state.zoom_level = 100
    with col3:
        if st.button("➕ Zoom In"):
            if st.session_state.zoom_level < 200:
                st.session_state.zoom_level += 10

# --- Main image display section ---
col1, col2 = st.columns([1, 5])
with col2:
    image_rendered = False

    # Live Map logic
    if st.session_state.selected_button == "Live Map":
        map_file = "delhi_dealer_district_map_with_images.html"
        if os.path.exists(map_file):
            st.markdown("### Dealer Map with Images")
            map_html = Path(map_file).read_text()
            st.components.v1.html(map_html, height=800, scrolling=True)
            image_rendered = True
        else:
            st.error("Map file 'delhi_dealer_district_map_with_images.html' not found.")
            image_rendered = True  # Prevent fallback image rendering

    # Static image viewer
    if not image_rendered and st.session_state.selected_button:
        selected_label = st.session_state.selected_button
        index = button_names.index(selected_label)
        image_name = image_files[index]
        image_folder = "YusufSaraiGEOIQImages"
        image_path = os.path.join(image_folder, f"{image_name}.png") if image_name else None

        if image_path and os.path.exists(image_path):
            # Load image with enhanced quality settings
            image = Image.open(image_path)

            # Enhance image quality for better zoom experience
            original_width, original_height = image.size
            if original_width < 1200 or original_height < 800:
                # Scale up smaller images for better zoom quality
                scale_factor = max(1200 / original_width, 800 / original_height)
                new_width = int(original_width * scale_factor)
                new_height = int(original_height * scale_factor)
                image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)

            encoded_image = image_to_base64(image, quality=98)
            st.markdown(f"### Image: {selected_label}")
            render_zoom_controls()
            zoom = st.session_state.zoom_level

            st.markdown(
                f'''
                <div class="zoom-container">
                <img src="data:image/png;base64,{encoded_image}"
                style="width: {zoom}%; height: auto; border: 1px solid #ddd; display: block;
                       image-rendering: -webkit-optimize-contrast;
                       image-rendering: -moz-crisp-edges;
                       image-rendering: crisp-edges;
                       image-rendering: high-quality;
                       -ms-interpolation-mode: bicubic;
                       backface-visibility: hidden;
                       -webkit-backface-visibility: hidden;
                       transform: translateZ(0);
                       -webkit-transform: translateZ(0);
                       -webkit-font-smoothing: antialiased;
                       -moz-osx-font-smoothing: grayscale;">
                </div>
                ''',
                unsafe_allow_html=True
            )

            image_rendered = True
        elif image_path:
            st.error(f"Image '{image_name}.png' not found in folder '{image_folder}'.")
            image_rendered = True

    # Sub-category fallback image
    if not image_rendered:
        subcat_name = st.session_state.sub_category
        image_filename = sub_category_mapping.get(subcat_name)

        if image_filename:
            image_folder = "YusufSaraiItemPrice"
            image_path = os.path.join(image_folder, f"{image_filename}.png")

            if os.path.exists(image_path):
                # Load sub-category image with enhanced quality settings
                image = Image.open(image_path)

                # Enhance image quality for better zoom experience
                original_width, original_height = image.size
                if original_width < 1200 or original_height < 800:
                    # Scale up smaller images for better zoom quality
                    scale_factor = max(1200 / original_width, 800 / original_height)
                    new_width = int(original_width * scale_factor)
                    new_height = int(original_height * scale_factor)
                    image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)

                encoded_image = image_to_base64(image, quality=98)

                st.markdown(f"### Sub-Category Image: {subcat_name}")
                render_zoom_controls()
                zoom = st.session_state.zoom_level

                st.markdown(
                    f'''
                    <div class="zoom-container" style="text-align:center; width: 100%;">
                        <img src="data:image/png;base64,{encoded_image}"
                             style="max-width: 100%; width: {zoom}%; height: auto; border: 1px solid #ddd;
                                    image-rendering: -webkit-optimize-contrast;
                                    image-rendering: -moz-crisp-edges;
                                    image-rendering: crisp-edges;
                                    image-rendering: high-quality;
                                    -ms-interpolation-mode: bicubic;
                                    backface-visibility: hidden;
                                    -webkit-backface-visibility: hidden;
                                    transform: translateZ(0);
                                    -webkit-transform: translateZ(0);
                                    -webkit-font-smoothing: antialiased;
                                    -moz-osx-font-smoothing: grayscale;">
                    </div>
                    ''',
                    unsafe_allow_html=True
                )
                image_rendered = True
            else:
                st.error(f"Sub-category image '{image_filename}.png' not found in folder '{image_folder}'.")

