{"name": "amaron-dashboard", "private": true, "version": "0.0.0", "homepage": "https://theprym.github.io/sales-ys", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "predeploy": "npm run build", "deploy": "gh-pages -d build", "start": "react-scripts start", "build2": "react-scripts build"}, "dependencies": {"html2canvas": "^1.4.1", "jspdf": "^2.5.1", "lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1", "recharts": "^2.8.0", "react-leaflet": "^4.2.1"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/leaflet": "^1.9.19", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "gh-pages": "^6.3.0", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}