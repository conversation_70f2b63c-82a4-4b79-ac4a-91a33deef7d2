import React, { useState, useRef, useEffect } from 'react';
import { ZoomIn, ZoomOut, RotateCcw, Move } from 'lucide-react';

interface ZoomableImageProps {
  src: string;
  alt: string;
  title: string;
  className?: string;
  height?: number;
}

export const ZoomableImage: React.FC<ZoomableImageProps> = ({
  src,
  alt,
  title,
  className = '',
  height = 650
}) => {
  const [zoomLevel, setZoomLevel] = useState(100);
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [scrollPos, setScrollPos] = useState({ x: 0, y: 0 });
  const containerRef = useRef<HTMLDivElement>(null);
  const imageRef = useRef<HTMLImageElement>(null);

  const handleZoomOut = () => {
    if (zoomLevel > 25) {
      setZoomLevel(Math.max(25, zoomLevel - 25));
    }
  };

  const handleZoomIn = () => {
    if (zoomLevel < 300) {
      setZoomLevel(Math.min(300, zoomLevel + 25));
    }
  };

  const handleReset = () => {
    setZoomLevel(100);
    if (containerRef.current) {
      containerRef.current.scrollLeft = 0;
      containerRef.current.scrollTop = 0;
    }
  };

  const handleMouseDown = (e: React.MouseEvent) => {
    if (zoomLevel > 100) {
      setIsDragging(true);
      setDragStart({
        x: e.pageX - (containerRef.current?.offsetLeft || 0),
        y: e.pageY - (containerRef.current?.offsetTop || 0)
      });
      setScrollPos({
        x: containerRef.current?.scrollLeft || 0,
        y: containerRef.current?.scrollTop || 0
      });
    }
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isDragging || zoomLevel <= 100) return;
    
    e.preventDefault();
    const x = e.pageX - (containerRef.current?.offsetLeft || 0);
    const y = e.pageY - (containerRef.current?.offsetTop || 0);
    const walkX = (x - dragStart.x) * 2;
    const walkY = (y - dragStart.y) * 2;
    
    if (containerRef.current) {
      containerRef.current.scrollLeft = scrollPos.x - walkX;
      containerRef.current.scrollTop = scrollPos.y - walkY;
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  useEffect(() => {
    const handleGlobalMouseUp = () => setIsDragging(false);
    document.addEventListener('mouseup', handleGlobalMouseUp);
    return () => document.removeEventListener('mouseup', handleGlobalMouseUp);
  }, []);

  const dragEnabled = zoomLevel > 100;
  const cursorStyle = dragEnabled ? (isDragging ? 'grabbing' : 'grab') : 'default';

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Image Title */}
      <h3 className="text-xl font-bold text-gray-800 flex items-center">
        📊 {title}
      </h3>

      {/* Zoomable Image Container */}
      <div
        ref={containerRef}
        className="relative border-2 border-orange-500 rounded-lg bg-gray-50 overflow-auto"
        style={{
          height: `${height}px`,
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)'
        }}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
      >
        {/* Zoom Info Badge */}
        <div
          className="absolute top-2 right-2 z-10 bg-white bg-opacity-95 px-3 py-2 rounded border border-orange-500 text-orange-600 font-bold text-sm shadow"
          style={{ fontSize: '14px' }}
        >
          📏 {zoomLevel}%
        </div>

        {/* Drag Instructions */}
        {dragEnabled && (
          <div
            className="absolute bottom-2 left-2 z-10 bg-white bg-opacity-95 px-2 py-1 rounded border border-gray-300 text-gray-700 text-xs shadow"
            style={{ fontSize: '12px' }}
          >
            🖱️ Click and drag to pan
          </div>
        )}

        {/* Zoomable Image */}
        <img
          ref={imageRef}
          src={src}
          alt={alt}
          className="block mx-auto transition-all duration-300"
          style={{
            width: `${zoomLevel}%`,
            height: 'auto',
            cursor: cursorStyle,
            imageRendering: 'high-quality',
            backfaceVisibility: 'hidden',
            transform: 'translateZ(0)',
            WebkitFontSmoothing: 'antialiased'
          }}
          draggable={false}
        />
      </div>

      {/* Zoom Controls */}
      <div className="bg-gradient-to-br from-gray-50 to-white p-4 rounded-lg border border-orange-500 shadow-lg">
        <h4 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
          🔍 Image Zoom & Navigation
        </h4>
        
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 items-center">
          {/* Zoom Out Button */}
          <button
            onClick={handleZoomOut}
            disabled={zoomLevel <= 25}
            className="flex items-center justify-center px-4 py-2 bg-orange-500 text-white rounded-md hover:bg-orange-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <ZoomOut className="w-4 h-4 mr-2" />
            🔍➖ Zoom Out
          </button>

          {/* Reset Button */}
          <button
            onClick={handleReset}
            className="flex items-center justify-center px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors"
          >
            <RotateCcw className="w-4 h-4 mr-2" />
            🔄 Reset
          </button>

          {/* Zoom In Button */}
          <button
            onClick={handleZoomIn}
            disabled={zoomLevel >= 300}
            className="flex items-center justify-center px-4 py-2 bg-orange-500 text-white rounded-md hover:bg-orange-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <ZoomIn className="w-4 h-4 mr-2" />
            🔍➕ Zoom In
          </button>

          {/* Zoom Slider */}
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-600 whitespace-nowrap">🎚️ Zoom Level</span>
            <input
              type="range"
              min="25"
              max="300"
              step="5"
              value={zoomLevel}
              onChange={(e) => setZoomLevel(parseInt(e.target.value))}
              className="flex-1 h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
              style={{
                background: `linear-gradient(to right, #ff6600 0%, #ff6600 ${((zoomLevel - 25) / (300 - 25)) * 100}%, #e5e7eb ${((zoomLevel - 25) / (300 - 25)) * 100}%, #e5e7eb 100%)`
              }}
            />
            <span className="text-sm text-gray-600 font-medium min-w-[3rem]">{zoomLevel}%</span>
          </div>
        </div>
      </div>
    </div>
  );
};
