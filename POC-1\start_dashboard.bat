@echo off
echo 🎯 Stonesbury Mimo Analytics Dashboard Launcher
echo ==================================================

echo 🔍 Checking dependencies...

:: Check if Python is available
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python not found. Please install Python 3.8 or higher.
    pause
    exit /b 1
)
echo ✅ Python found

:: Check if Node.js is available
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js not found. Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)
echo ✅ Node.js found

:: Check if npm is available
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm not found. Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)
echo ✅ npm found

:: Install Node.js dependencies if needed
if not exist "node_modules" (
    echo 📦 Installing Node.js dependencies...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ Failed to install Node.js dependencies
        pause
        exit /b 1
    )
    echo ✅ Node.js dependencies installed
) else (
    echo ✅ Node.js dependencies already installed
)

echo.
echo 🚀 Starting Flask backend server...
start "Flask Backend" cmd /k "python flask_backend.py"

echo ⏳ Waiting for Flask backend to start...
timeout /t 5 /nobreak >nul

echo 🚀 Starting React frontend server...
start "React Frontend" cmd /k "npm run dev"

echo.
echo 🎉 Both servers are starting!
echo 📱 Frontend: http://localhost:3002
echo 🔧 Backend API: http://localhost:5000
echo.
echo 🔑 Login credentials:
echo    - <EMAIL> / user1234
echo    - <EMAIL> / user1234
echo    - <EMAIL> / user1234
echo.
echo ⏹️  Close the terminal windows to stop the servers
echo.
pause
