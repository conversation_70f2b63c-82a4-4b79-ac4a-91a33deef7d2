@echo off
echo 🔧 POC-1 Dashboard Startup (Windows)
echo ==========================================

REM Check if we're in the right directory
if not exist "flask_backend.py" (
    echo ❌ flask_backend.py not found!
    echo    Please run this script from the POC-1 directory
    pause
    exit /b 1
)

if not exist "package.json" (
    echo ❌ package.json not found!
    echo    Please run this script from the POC-1 directory
    pause
    exit /b 1
)

echo 📦 Installing dependencies...

REM Install Python dependencies
echo Installing Python dependencies...
pip install -r flask_requirements.txt
if errorlevel 1 (
    echo ❌ Failed to install Python dependencies
    pause
    exit /b 1
)

REM Install Node.js dependencies
echo Installing Node.js dependencies...
call npm install
if errorlevel 1 (
    echo ❌ Failed to install Node.js dependencies
    echo 💡 Make sure Node.js is installed and npm is in your PATH
    pause
    exit /b 1
)

REM Create necessary directories
if not exist "submitted_forms" mkdir submitted_forms

echo.
echo 🚀 Starting servers...
echo ==========================================

REM Start Flask backend in a new window
echo Starting Flask backend...
start "Flask Backend" cmd /k "python flask_backend.py"

REM Wait a moment for Flask to start
timeout /t 3 /nobreak >nul

REM Start React frontend in a new window
echo Starting React frontend...
start "React Frontend" cmd /k "npm run dev"

echo.
echo ==========================================
echo 🎉 POC-1 Dashboard is starting!
echo ==========================================
echo 📍 Frontend: http://localhost:3000
echo 📍 Backend: http://localhost:5000
echo.
echo 🔐 Login Credentials:
echo    Email: <EMAIL>
echo    Password: user
echo.
echo ==========================================
echo Two new windows should have opened:
echo 1. Flask Backend (port 5000)
echo 2. React Frontend (port 3000)
echo.
echo Close those windows to stop the servers.
echo ==========================================

pause
