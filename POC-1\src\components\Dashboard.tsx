import React, { useState, useEffect } from 'react';
import { useAuth } from './AuthProvider';
import { DealerMap } from './DealerMap';
import { ImageGallery } from './ImageGallery';
import { MimoForm } from './MimoForm';
import { ZoomableImage } from './ZoomableImage';
import { StrategicReport } from './StrategicReport';
import { apiService } from '../services/api';
import {
  Map,
  Images,
  FileText,
  LogOut,
  User,
  Home,
  BarChart3,
  Search,
  Eye,
  Settings
} from 'lucide-react';

// Match Streamlit's page structure
type TabType = 'overview' | 'map' | 'gallery' | 'form' | 'analytics';

// Geographic data matching Streamlit app
const ALL_STATES = [
  "Delhi(NCT)", "West Bengal", "Andhra Pradesh", "Arunachal Pradesh", "Assam",
  "Bihar", "Chhattisgarh", "Goa", "Gujarat", "Haryana", "Himachal Pradesh",
  "Jharkhand", "Jammu and Kashmir", "Karnataka", "Kerala", "Ladakh",
  "Lakshadweep", "Madhya Pradesh", "Maharashtra", "Manipur", "Meghalaya",
  "Mizoram", "Nagaland", "Odisha", "Punjab", "Puducherry", "Rajasthan",
  "Sikkim", "Tamil Nadu", "Telangana", "Tripura", "Uttar Pradesh",
  "Uttarakhand"
];

// Valid geographic combinations matching Streamlit app
const VALID_GEOGRAPHICAL_COMBINATIONS = [
  ["Delhi(NCT)", "South Central", "Yusuf Sarai"],
  ["West Bengal", "East", "South 24 Parganas"]
];

export const Dashboard: React.FC = () => {
  const { user, logout } = useAuth();
  const [activeTab, setActiveTab] = useState<TabType>('overview');

  // Geographic state matching Streamlit's session_state
  const [selectedState, setSelectedState] = useState<string>("Delhi(NCT)");
  const [selectedGeography, setSelectedGeography] = useState<string>("South Central");
  const [selectedLocality, setSelectedLocality] = useState<string>("Yusuf Sarai");
  const [isValidCombination, setIsValidCombination] = useState<boolean>(true);

  // Additional Streamlit features
  const [showCategories, setShowCategories] = useState<boolean>(false);
  const [selectedCategory, setSelectedCategory] = useState<string>("Select");
  const [selectedSubCategory, setSelectedSubCategory] = useState<string>("Select");
  const [showCategoryResults, setShowCategoryResults] = useState<boolean>(false);
  const [brandSearchQuery, setBrandSearchQuery] = useState<string>("");
  const [showCompetitors, setShowCompetitors] = useState<boolean>(false);
  const [showStrategicReport, setShowStrategicReport] = useState<boolean>(false);
  const [currentImage, setCurrentImage] = useState<string | null>(null);
  const [currentImageTitle, setCurrentImageTitle] = useState<string>("");

  const handleFormSubmit = async (formData: any) => {
    try {
      const response = await apiService.submitMimoForm(formData);
      return response;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Submission failed'
      };
    }
  };

  // Streamlit-style sidebar sections
  const sidebarSections = [
    { id: 'overview', label: 'Dashboard Overview', icon: Home },
    { id: 'map', label: 'Interactive Map', icon: Map },
    { id: 'gallery', label: 'Image Gallery', icon: Images },
    { id: 'form', label: 'Mimo Form', icon: FileText },
  ];

  // Category and subcategory data matching Streamlit app exactly
  const categories = [
    "Select", "Appliances", "Lighting", "Electronics", "Services"
  ];

  const subCategories: { [key: string]: string[] } = {
    "Appliances": ["Select", "Ceiling Fans", "Food Processors", "Air Conditioners", "Refrigerators"],
    "Lighting": ["Select", "Room Lights", "Light Holders", "Desk Lights", "Solar Lights"],
    "Electronics": ["Select", "Mobile Accessories", "Computers", "Audio Systems", "Gaming"],
    "Services": ["Select", "Electricians", "Plumbers", "Technicians", "Consultants"]
  };

  // Image mapping for categories (matching Streamlit's sub_category_mapping)
  const subCategoryImageMapping: { [key: string]: string } = {
    "Ceiling Fans": "CeilingFans",
    "Food Processors": "FoodProcessors",
    "Room Lights": "RoomLights",
    "Light Holders": "LightHolders",
    "Desk Lights": "DeskLights",
    "Solar Lights": "SolarLights",
    "Mobile Accessories": "MobileAccessories",
    "Electricians": "Electricians",
    "Plumbers": "Plumbers"
  };

  // Validate geographic combination
  useEffect(() => {
    const currentCombination = [selectedState, selectedGeography, selectedLocality];
    const isValid = VALID_GEOGRAPHICAL_COMBINATIONS.some(
      validCombo => JSON.stringify(validCombo) === JSON.stringify(currentCombination)
    );
    setIsValidCombination(isValid);
  }, [selectedState, selectedGeography, selectedLocality]);

  const renderMainContent = () => {
    if (!isValidCombination) {
      return (
        <div className="flex items-center justify-center h-96">
          <div className="text-center p-24 border-2 border-dashed border-orange-500 rounded-lg bg-gradient-to-br from-white to-gray-50 shadow-lg">
            <h3 className="text-4xl font-bold text-red-600 mb-0" style={{ color: '#DC143C' }}>
              Libraries are not connected
            </h3>
          </div>
        </div>
      );
    }

    if (activeTab === 'map') {
      return <DealerMap />;
    }

    if (activeTab === 'gallery') {
      return <ImageGallery searchQuery={brandSearchQuery} showCompetitors={showCompetitors} />;
    }

    if (activeTab === 'form') {
      return <MimoForm onSubmit={handleFormSubmit} />;
    }

    // Show current image if one is selected
    if (currentImage && currentImageTitle) {
      return (
        <ZoomableImage
          src={currentImage}
          alt={currentImageTitle}
          title={currentImageTitle}
          height={650}
        />
      );
    }

    // Default welcome message (matching Streamlit)
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center p-40 border-2 border-dashed border-orange-500 rounded-lg bg-white shadow-lg">
          <h2 className="text-4xl font-bold text-orange-500 mb-5">
            Welcome to Stonesbury Mimo Analytics Dashboard
          </h2>
          <p className="text-xl text-gray-600 leading-relaxed">
            Select an analytics view from the sidebar to explore geographic insights,
            or choose a product/service category to view detailed market information.
          </p>
        </div>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gray-50 flex">
      {/* Streamlit-style Sidebar */}
      <div className="w-80 bg-white border-r border-gray-200 flex flex-col">
        {/* Logo Section */}
        <div className="p-6 border-b border-gray-200">
          <img src="logo/Stonesbury-logo.png" alt="Stonesbury Logo" className="h-12 w-auto mb-4" />
          <h1 className="text-xl font-bold text-gray-800">Stonesbury Mimo Analytics Dashboard</h1>
        </div>

        {/* Geographic Intelligence Section */}
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-800 mb-4">🌍 Geographic Intelligence</h2>

          {/* State Selection */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">State</label>
            <select
              value={selectedState}
              onChange={(e) => setSelectedState(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 outline-none"
            >
              {ALL_STATES.map(state => (
                <option key={state} value={state}>{state}</option>
              ))}
            </select>
          </div>

          {/* Geography Selection */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">Geography</label>
            <select
              value={selectedGeography}
              onChange={(e) => setSelectedGeography(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 outline-none"
            >
              <option value="South Central">South Central</option>
              <option value="East">East</option>
              <option value="North">North</option>
              <option value="West">West</option>
            </select>
          </div>

          {/* Locality Selection */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">Locality</label>
            <select
              value={selectedLocality}
              onChange={(e) => setSelectedLocality(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 outline-none"
            >
              <option value="Yusuf Sarai">Yusuf Sarai</option>
              <option value="South 24 Parganas">South 24 Parganas</option>
              <option value="Central Delhi">Central Delhi</option>
              <option value="Kolkata">Kolkata</option>
            </select>
          </div>
        </div>

        {/* Brand Search Section */}
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">🔍 Brand Search</h3>

          <div className="space-y-4">
            {/* Brand Search Input */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Search Brand</label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="Enter brand name (Amaron, Exide, Tata)..."
                  value={brandSearchQuery}
                  onChange={(e) => setBrandSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 outline-none text-sm"
                />
              </div>
            </div>

            {/* Show Competitors Toggle */}
            <div className="flex items-center">
              <input
                type="checkbox"
                id="showCompetitorsSidebar"
                checked={showCompetitors}
                onChange={(e) => setShowCompetitors(e.target.checked)}
                className="mr-2 w-4 h-4 text-orange-600 bg-gray-100 border-gray-300 rounded focus:ring-orange-500"
              />
              <label htmlFor="showCompetitorsSidebar" className="text-sm text-gray-700">
                Show All Competitors
              </label>
            </div>

            {/* Open Strategic Report Button */}
            <button
              onClick={() => setShowStrategicReport(true)}
              className="w-full flex items-center justify-center px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors text-sm"
            >
              <Eye className="w-4 h-4 mr-2" />
              📊 Open Strategic Report
            </button>
          </div>
        </div>

        {/* Categories Section */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-800">🏷️ Product/Service Categories</h3>
            <button
              onClick={() => setShowCategories(!showCategories)}
              className="text-orange-600 hover:text-orange-700 transition-colors"
            >
              <Settings className="w-5 h-5" />
            </button>
          </div>

          {showCategories && (
            <div className="space-y-4">
              {/* Category Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Category</label>
                <select
                  value={selectedCategory}
                  onChange={(e) => {
                    setSelectedCategory(e.target.value);
                    setSelectedSubCategory("Select");
                    setShowCategoryResults(false);
                  }}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 outline-none text-sm"
                >
                  {categories.map(category => (
                    <option key={category} value={category}>{category}</option>
                  ))}
                </select>
              </div>

              {/* Sub-Category Selection */}
              {selectedCategory !== "Select" && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Product/Service Type</label>
                  <select
                    value={selectedSubCategory}
                    onChange={(e) => {
                      setSelectedSubCategory(e.target.value);
                      setShowCategoryResults(false);
                    }}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 outline-none text-sm"
                  >
                    {(subCategories[selectedCategory] || []).map(subCat => (
                      <option key={subCat} value={subCat}>{subCat}</option>
                    ))}
                  </select>
                </div>
              )}

              {/* Go Button */}
              {selectedCategory !== "Select" && selectedSubCategory !== "Select" && (
                <button
                  onClick={() => {
                    setShowCategoryResults(true);
                    const imageFile = subCategoryImageMapping[selectedSubCategory];
                    if (imageFile) {
                      // In a real app, you'd load the actual image
                      setCurrentImage(`/api/images/${imageFile}.png`);
                      setCurrentImageTitle(selectedSubCategory);
                    }
                  }}
                  className="w-full px-4 py-2 bg-orange-500 text-white rounded-md hover:bg-orange-600 transition-colors text-sm font-medium"
                >
                  Go
                </button>
              )}
            </div>
          )}
        </div>

        {/* Navigation Section */}
        <div className="p-6 flex-1">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">📊 Analytics Views</h3>
          <nav className="space-y-2">
            {sidebarSections.map((section) => {
              const Icon = section.icon;
              return (
                <button
                  key={section.id}
                  onClick={() => setActiveTab(section.id as TabType)}
                  className={`w-full flex items-center px-4 py-3 text-left rounded-md transition-colors ${
                    activeTab === section.id
                      ? 'bg-orange-50 text-orange-600 border border-orange-200'
                      : 'text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  <Icon className="w-5 h-5 mr-3" />
                  {section.label}
                </button>
              );
            })}
          </nav>
        </div>

        {/* User Info Section */}
        <div className="p-6 border-t border-gray-200">
          <div className="flex items-center mb-4">
            <User className="w-8 h-8 text-gray-400 mr-3" />
            <div>
              <p className="text-sm font-medium text-gray-800">{user?.email}</p>
              <p className="text-xs text-gray-500">Authenticated User</p>
            </div>
          </div>
          <button
            onClick={logout}
            className="w-full flex items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50 rounded-md transition-colors"
          >
            <LogOut className="w-4 h-4 mr-2" />
            Sign Out
          </button>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col">
        {/* Top Header */}
        <div className="bg-white border-b border-gray-200 px-6 py-4">
          <h1 className="text-2xl font-bold text-gray-800">
            Stonesbury Mimo Analytics Dashboard
          </h1>
          <p className="text-gray-600 mt-1">
            Geographic Intelligence and Market Analytics Platform
          </p>
        </div>

        {/* Main Content */}
        <div className="flex-1 p-6 bg-gray-50">
          {renderMainContent()}
        </div>
      </div>

      {/* Strategic Report Modal */}
      {showStrategicReport && (
        <StrategicReport
          state={selectedState}
          onClose={() => setShowStrategicReport(false)}
        />
      )}
    </div>
  );
};
