import React, { useState, useEffect } from 'react';
import { useAuth } from './AuthProvider';
// import { DealerMap } from './DealerMap';
// import { ImageGallery } from './ImageGallery';
import { MimoForm } from './MimoForm';

import { StrategicReport } from './StrategicReport';
import { AnalyticsImage } from './AnalyticsImage';
import { CategoryImage } from './CategoryImage';
import { InteractiveMap } from './InteractiveMap';
import { apiService } from '../services/api';
import {
  Map,
  Images,
  FileText,
  LogOut,
  User,
  Home
} from 'lucide-react';

// Helper function to parse URL parameters
const getUrlParams = () => {
  const params = new URLSearchParams(window.location.search);
  return {
    tab: params.get('tab'),
    kpi: params.get('kpi'),
    category: params.get('category'),
    subcategory: params.get('subcategory'),
    row: params.get('row')
  };
};

// Match Streamlit's page structure
type TabType = 'overview' | 'map' | 'gallery' | 'form' | 'analytics' | 'categories' | 'interactive-map';

// Geographic data matching Streamlit app
const ALL_STATES = [
  "Delhi(NCT)", "West Bengal", "Andhra Pradesh", "Arunachal Pradesh", "Assam",
  "Bihar", "Chhattisgarh", "Goa", "Gujarat", "Haryana", "Himachal Pradesh",
  "Jharkhand", "Jammu and Kashmir", "Karnataka", "Kerala", "Ladakh",
  "Lakshadweep", "Madhya Pradesh", "Maharashtra", "Manipur", "Meghalaya",
  "Mizoram", "Nagaland", "Odisha", "Punjab", "Puducherry", "Rajasthan",
  "Sikkim", "Tamil Nadu", "Telangana", "Tripura", "Uttar Pradesh",
  "Uttarakhand"
];

// Valid geographic combinations matching Streamlit app
const VALID_GEOGRAPHICAL_COMBINATIONS = [
  ["Delhi(NCT)", "South Central", "Yusuf Sarai"],
  ["West Bengal", "East", "South 24 Parganas"]
];

export const Dashboard: React.FC = () => {
  console.log('Dashboard component rendering...');
  const { user, logout } = useAuth();

  // Check URL parameters for initial state
  const urlParams = getUrlParams();
  const [activeTab, setActiveTab] = useState<TabType>(urlParams.tab as TabType || 'overview');

  // Geographic state matching Streamlit's session_state
  const [selectedState, setSelectedState] = useState<string>("Delhi(NCT)");
  const [selectedGeography, setSelectedGeography] = useState<string>("South Central");
  const [selectedLocality, setSelectedLocality] = useState<string>("Yusuf Sarai");
  const [isValidCombination, setIsValidCombination] = useState<boolean>(true);

  // Additional Streamlit features
  const [brandSearchQuery, setBrandSearchQuery] = useState<string>("All Brands");
  const [showStrategicReport, setShowStrategicReport] = useState<boolean>(false);
  const [selectedAnalyticsView, setSelectedAnalyticsView] = useState<string | null>(null);

  // Mimo Form initial data from URL parameters
  const [mimoFormInitialData, setMimoFormInitialData] = useState<any>({});

  // Business Category functionality - Exact match with Streamlit
  const [selectedBusinessCategory, setSelectedBusinessCategory] = useState<string>("Products");
  const [showCategories, setShowCategories] = useState<boolean>(false);
  const [selectedCategory, setSelectedCategory] = useState<string>("");
  const [selectedSubCategory, setSelectedSubCategory] = useState<string>("Select");
  const [showCategoryResults, setShowCategoryResults] = useState<boolean>(false);

  // Interactive Map functionality - Exact match with Streamlit
  const [showInteractiveMap, setShowInteractiveMap] = useState<boolean>(false);

  const handleFormSubmit = async (formData: any) => {
    try {
      const response = await apiService.submitMimoForm(formData);
      return response;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Submission failed'
      };
    }
  };

  // Streamlit-style sidebar sections
  const sidebarSections = [
    { id: 'overview', label: 'Dashboard Overview', icon: Home },
    { id: 'map', label: 'Interactive Map', icon: Map },
    { id: 'gallery', label: 'Image Gallery', icon: Images },
    { id: 'form', label: 'Mimo Form', icon: FileText },
  ];

  // Analytics Views - Exact match with Streamlit
  const analyticsViews = [
    "Households 5L+", "Households 10L+", "Households 20L+", "Trade Area Analysis",
    "Total Footfall", "Branded Footfall", "Hourly Footfall", "Daily Footfall",
    "Weekday vs Weekend", "Category Analysis", "Customer Origins"
  ];

  // Business Categories - Exact match with Streamlit
  const businessCategories = ["Products", "Services"];

  // Brand options - Exact match with Streamlit
  const brandOptions = [
    "All Brands", "Amaron", "Exide", "Tata Green", "Luminous", "Microtek", "Su-Kam"
  ];

  // Button to image mapping - Exact match with Streamlit
  const analyticsImageMapping: { [key: string]: string } = {
    "Households 5L+": "YSHH5LA-1",
    "Households 10L+": "YSHH10LA-2",
    "Households 20L+": "YSHH20LA-3",
    "Trade Area Analysis": "YSTA-4",
    "Total Footfall": "YSTFF-5",
    "Branded Footfall": "YSBFF-6",
    "Hourly Footfall": "YSHFF-7",
    "Daily Footfall": "YSDFF-8",
    "Weekday vs Weekend": "YSWWFF-9",
    "Category Analysis": "YSCM-10",
    "Customer Origins": "YSWSCF-11"
  };

  // Sub-category image mapping - Exact match with Streamlit
  const subCategoryMapping: { [key: string]: string } = {
    "Exhaust Fans": "ExhaustFans",
    "Ceiling Fans": "CeilingFans",
    "Room Lights": "RoomLights",
    "Light Holders": "LightHolders",
    "Desk Lights": "DeskLights",
    "Solar Lights": "SolarLights",
    "Food Processor": "FoodProcessor",
    "Home Tools": "HomeTools",
    "BT SPK Sets / Radio": "BtSpkSetsRadio",
    "Ed Electronics": "EdElectronics",
    "Phone Accessories": "PhoneAccesories",
    "Electricians": "Electricians",
    "Plumbers": "Plumbers"
  };

  // Subcategory options - Exact match with Streamlit (state-specific)
  const getSubcategoryOptions = (state: string) => {
    if (state === "Delhi(NCT)") {
      return {
        "Products": {
          "Electronics & H. Appliances": [
            "Select", "Exhaust Fans", "Ceiling Fans", "Room Lights", "Light Holders",
            "Desk Lights", "Solar Lights", "Food Processor", "Home Tools",
            "BT SPK Sets / Radio", "Ed Electronics", "Phone Accessories"
          ],
          "Fashion & Apparels": [
            "Select", "Clothing", "Accessories", "Shoes", "Bags"
          ],
          "Jewelery": [
            "Select", "Gold Jewelery", "Silver Jewelery", "Fashion Jewelery", "Watches"
          ],
          "Home Furnishing": [
            "Select", "Furniture", "Curtains", "Bedding", "Decor Items"
          ],
          "Footwear": [
            "Select", "Formal Shoes", "Casual Shoes", "Sports Shoes", "Sandals"
          ]
        },
        "Services": {
          "Services": ["Electricians", "Mason", "Lawyer", "Welder", "Auto mechanic", "Plumbers"]
        }
      };
    } else if (state === "West Bengal") {
      return {
        "Products": {
          "Electronics & H. Appliances": [
            "Select", "Exhaust Fans", "Ceiling Fans", "Room Lights", "Light Holders",
            "Desk Lights", "Solar Lights", "Food Processor", "Home Tools",
            "BT SPK Sets / Radio", "Ed Electronics", "Phone Accessories"
          ],
          "Fashion & Apparels": [
            "Select", "Clothing", "Accessories", "Shoes", "Bags"
          ],
          "Jewelery": [
            "Select", "Gold Jewelery", "Silver Jewelery", "Fashion Jewelery", "Watches"
          ],
          "Home Furnishing": [
            "Select", "Furniture", "Curtains", "Bedding", "Decor Items"
          ],
          "Footwear": [
            "Select", "Formal Shoes", "Casual Shoes", "Sports Shoes", "Sandals"
          ],
          "Food & Service": [
            "Select", "Packaged Food", "Fresh Food", "Beverages", "Snacks"
          ]
        },
        "Services": {
          "Services": ["Electricians", "Mason", "Lawyer", "Welder", "Auto mechanic", "Plumbers"],
          "Restaurant": [
            "Select", "Fine Dining", "Fast Food", "Cafe", "Street Food"
          ]
        }
      };
    }
    return {};
  };



  // Handle URL parameters for Mimo Form
  useEffect(() => {
    const params = getUrlParams();

    // If we have URL parameters for the form, set up initial data
    if (params.kpi || params.category || params.subcategory) {
      setMimoFormInitialData({
        kpi: params.kpi || '',
        category: params.category || '',
        subcategory: params.subcategory || '',
        row: params.row || ''
      });

      // If tab parameter is 'form', switch to form tab
      if (params.tab === 'form') {
        setActiveTab('form');
      }

      console.log('Mimo Form initialized with URL parameters:', params);
    }
  }, []);

  // Validate geographic combination
  useEffect(() => {
    const currentCombination = [selectedState, selectedGeography, selectedLocality];
    const isValid = VALID_GEOGRAPHICAL_COMBINATIONS.some(
      validCombo => JSON.stringify(validCombo) === JSON.stringify(currentCombination)
    );
    setIsValidCombination(isValid);
  }, [selectedState, selectedGeography, selectedLocality]);

  const renderMainContent = () => {
    if (!isValidCombination) {
      return (
        <div className="flex items-center justify-center h-96">
          <div className="text-center p-24 border-2 border-dashed border-orange-500 rounded-lg bg-gradient-to-br from-white to-gray-50 shadow-lg">
            <h3 className="text-4xl font-bold text-red-600 mb-0" style={{ color: '#DC143C' }}>
              Libraries are not connected
            </h3>
          </div>
        </div>
      );
    }

    if (activeTab === 'map') {
      return (
        <div className="p-8 text-center">
          <h2 className="text-2xl font-bold text-gray-800 mb-4">🗺️ Interactive Map</h2>
          <p className="text-gray-600">Map component loading...</p>
        </div>
      );
    }

    if (activeTab === 'gallery') {
      return (
        <div className="p-8 text-center">
          <h2 className="text-2xl font-bold text-gray-800 mb-4">🖼️ Image Gallery</h2>
          <p className="text-gray-600">Gallery component loading...</p>
        </div>
      );
    }

    if (activeTab === 'form') {
      return <MimoForm onSubmit={handleFormSubmit} initialData={mimoFormInitialData} />;
    }

    if (activeTab === 'analytics' && selectedAnalyticsView) {
      const imageName = analyticsImageMapping[selectedAnalyticsView];
      return (
        <AnalyticsImage
          viewName={selectedAnalyticsView}
          imageName={imageName}
          state={selectedState}
        />
      );
    }

    if (activeTab === 'interactive-map' && showInteractiveMap) {
      return (
        <InteractiveMap
          selectedBrand={brandSearchQuery}
          selectedState={selectedState}
          selectedGeography={selectedGeography}
          selectedLocality={selectedLocality}
          onBack={() => {
            setShowInteractiveMap(false);
            setActiveTab('overview');
          }}
        />
      );
    }

    if (activeTab === 'categories' && showCategoryResults) {
      const imageName = subCategoryMapping[selectedSubCategory];

      if (imageName) {
        return (
          <CategoryImage
            subcategory={selectedSubCategory}
            imageName={imageName}
            state={selectedState}
            businessCategory={selectedBusinessCategory}
            category={selectedCategory}
          />
        );
      } else {
        return (
          <div className="p-8">
            <h2 className="text-2xl font-bold text-gray-800 mb-6">
              📦 {selectedBusinessCategory} - {selectedCategory}
            </h2>
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <h3 className="text-lg font-semibold text-gray-800 mb-4">
                Selected: {selectedSubCategory}
              </h3>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-gray-700">Business Type:</span>
                    <span className="ml-2 text-gray-600">{selectedBusinessCategory}</span>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">Category:</span>
                    <span className="ml-2 text-gray-600">{selectedCategory}</span>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">Product/Service:</span>
                    <span className="ml-2 text-gray-600">{selectedSubCategory}</span>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">State:</span>
                    <span className="ml-2 text-gray-600">{selectedState}</span>
                  </div>
                </div>

                <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <p className="text-yellow-800 text-sm">
                    ⚠️ No image mapping found for "{selectedSubCategory}". This subcategory may not have an associated image.
                  </p>
                </div>
              </div>
            </div>
          </div>
        );
      }
    }

    // Default welcome message (matching Streamlit)
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center p-40 border-2 border-dashed border-orange-500 rounded-lg bg-white shadow-lg">
          <h2 className="text-4xl font-bold text-orange-500 mb-5">
            Welcome to Stonesbury Mimo Analytics Dashboard
          </h2>
          <p className="text-xl text-gray-600 leading-relaxed">
            Select an analytics view from the sidebar to explore geographic insights,
            or choose a product/service category to view detailed market information.
          </p>
        </div>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gray-50 flex">
      {/* Streamlit-style Sidebar */}
      <div className="w-80 bg-white border-r border-gray-200 flex flex-col">
        {/* Logo Section */}
        <div className="p-6 border-b border-gray-200">
          <img src="logo/Stonesbury-logo.png" alt="Stonesbury Logo" className="h-12 w-auto mb-4" />
          <h1 className="text-xl font-bold text-gray-800">Stonesbury Mimo Analytics Dashboard</h1>
        </div>

        {/* Geographic Intelligence Section */}
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-800 mb-4">🌍 Geographic Intelligence</h2>

          {/* State Selection */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">State</label>
            <select
              value={selectedState}
              onChange={(e) => setSelectedState(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 outline-none"
            >
              {ALL_STATES.map(state => (
                <option key={state} value={state}>{state}</option>
              ))}
            </select>
          </div>

          {/* Geography Selection */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">Geography</label>
            <select
              value={selectedGeography}
              onChange={(e) => setSelectedGeography(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 outline-none"
            >
              <option value="South Central">South Central</option>
              <option value="East">East</option>
              <option value="North">North</option>
              <option value="West">West</option>
            </select>
          </div>

          {/* Locality Selection */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">Locality</label>
            <select
              value={selectedLocality}
              onChange={(e) => setSelectedLocality(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 outline-none"
            >
              <option value="Yusuf Sarai">Yusuf Sarai</option>
              <option value="South 24 Parganas">South 24 Parganas</option>
              <option value="Central Delhi">Central Delhi</option>
              <option value="Kolkata">Kolkata</option>
            </select>
          </div>
        </div>

        {/* Analytics Views Section - Exact match with Streamlit */}
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">📊 Analytics Views</h3>

          <div className="grid grid-cols-2 gap-2">
            {analyticsViews.map((view) => (
              <button
                key={view}
                onClick={() => {
                  setSelectedAnalyticsView(view);
                  setActiveTab('analytics'); // Switch to analytics view
                }}
                className={`px-3 py-2 text-xs border rounded hover:bg-orange-50 transition-colors text-center ${
                  selectedAnalyticsView === view
                    ? 'bg-orange-500 text-white border-orange-500'
                    : 'bg-white border-orange-500 text-orange-600'
                }`}
              >
                {view}
              </button>
            ))}
          </div>

          {/* Detailed Analytics Button */}
          <button
            onClick={() => {
              console.log('Detailed Analytics clicked');
              // Open detailed analytics in new tab
              const analyticsUrl = 'http://localhost:5000/detailed-analytics';
              window.open(analyticsUrl, '_blank');
            }}
            className="w-full mt-4 px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600 transition-colors text-sm font-medium"
          >
            📊 Detailed Analytics
          </button>
        </div>

        {/* Business Category Section - Exact match with Streamlit */}
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">🏢 Business Category</h3>

          <div className="space-y-3">
            {businessCategories.map((category) => (
              <div key={category} className="flex items-center">
                <input
                  type="radio"
                  id={category}
                  name="businessCategory"
                  value={category}
                  checked={selectedBusinessCategory === category}
                  onChange={(e) => {
                    setSelectedBusinessCategory(e.target.value);
                    // Reset category selections when changing business category
                    setSelectedCategory("");
                    setSelectedSubCategory("Select");
                    setShowCategoryResults(false);
                  }}
                  className="mr-2 w-4 h-4 text-orange-600 bg-gray-100 border-gray-300 focus:ring-orange-500"
                />
                <label htmlFor={category} className="text-sm text-gray-700">
                  {category}
                </label>
              </div>
            ))}
          </div>

          {/* Product & Service Categories Button */}
          <button
            onClick={() => {
              setShowCategories(!showCategories);
              // Reset results when toggling categories
              setShowCategoryResults(false);
            }}
            className={`w-full mt-4 px-4 py-2 rounded transition-colors text-sm font-medium ${
              showCategories
                ? 'bg-orange-600 text-white'
                : 'bg-orange-500 text-white hover:bg-orange-600'
            }`}
          >
            🔗 Product & Service Categories
          </button>
        </div>

        {/* Product & Service Categories Interface - Exact match with Streamlit */}
        {showCategories && (
          <div className="p-6 border-b border-gray-200 bg-gray-50">
            <h4 className="text-lg font-semibold text-gray-800 mb-4">Product & Service Categories</h4>

            {(() => {
              const subcategoryOptions = getSubcategoryOptions(selectedState) as any;
              const availableCategories = Object.keys(subcategoryOptions[selectedBusinessCategory] || {});

              if (availableCategories.length === 0) {
                return (
                  <p className="text-gray-600 text-sm">No categories available for {selectedBusinessCategory} in {selectedState}</p>
                );
              }

              return (
                <div className="space-y-4">
                  {/* Category Selection */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Category:</label>
                    <select
                      value={selectedCategory}
                      onChange={(e) => {
                        setSelectedCategory(e.target.value);
                        setSelectedSubCategory("Select");
                        setShowCategoryResults(false);
                      }}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 outline-none text-sm"
                    >
                      <option value="">Select Category</option>
                      {availableCategories.map((category) => (
                        <option key={category} value={category}>{category}</option>
                      ))}
                    </select>
                  </div>

                  {/* Subcategory Selection */}
                  {selectedCategory && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Product/Service Type:</label>
                      <select
                        value={selectedSubCategory}
                        onChange={(e) => {
                          setSelectedSubCategory(e.target.value);
                          setShowCategoryResults(false);
                        }}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 outline-none text-sm"
                      >
                        {(subcategoryOptions[selectedBusinessCategory]?.[selectedCategory] || []).map((subcat: string) => (
                          <option key={subcat} value={subcat}>{subcat}</option>
                        ))}
                      </select>
                    </div>
                  )}

                  {/* Go Button */}
                  {selectedCategory && selectedSubCategory && selectedSubCategory !== "Select" && (
                    <button
                      onClick={() => {
                        setShowCategoryResults(true);
                        setActiveTab('categories'); // Switch to categories view
                      }}
                      className="w-full px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600 transition-colors text-sm font-medium"
                    >
                      Go
                    </button>
                  )}
                </div>
              );
            })()}
          </div>
        )}

        {/* Brand Search Section - Exact match with Streamlit */}
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">Brand Search:</h3>

          <div className="space-y-4">
            {/* Brand Dropdown */}
            <div>
              <select
                value={brandSearchQuery}
                onChange={(e) => setBrandSearchQuery(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 outline-none text-sm"
              >
                {brandOptions.map((brand) => (
                  <option key={brand} value={brand}>{brand}</option>
                ))}
              </select>
            </div>

            {/* Open Button */}
            <button
              onClick={() => {
                setShowInteractiveMap(true);
                setActiveTab('interactive-map');
              }}
              className="w-full px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600 transition-colors text-sm font-medium"
            >
              Open
            </button>
          </div>
        </div>



        {/* Navigation Section */}
        <div className="p-6 flex-1">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">📊 Analytics Views</h3>
          <nav className="space-y-2">
            {sidebarSections.map((section) => {
              const Icon = section.icon;
              return (
                <button
                  key={section.id}
                  onClick={() => setActiveTab(section.id as TabType)}
                  className={`w-full flex items-center px-4 py-3 text-left rounded-md transition-colors ${
                    activeTab === section.id
                      ? 'bg-orange-50 text-orange-600 border border-orange-200'
                      : 'text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  <Icon className="w-5 h-5 mr-3" />
                  {section.label}
                </button>
              );
            })}
          </nav>
        </div>

        {/* User Info Section */}
        <div className="p-6 border-t border-gray-200">
          <div className="flex items-center mb-4">
            <User className="w-8 h-8 text-gray-400 mr-3" />
            <div>
              <p className="text-sm font-medium text-gray-800">{user?.email}</p>
              <p className="text-xs text-gray-500">Authenticated User</p>
            </div>
          </div>
          <button
            onClick={logout}
            className="w-full flex items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50 rounded-md transition-colors"
          >
            <LogOut className="w-4 h-4 mr-2" />
            Sign Out
          </button>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col">
        {/* Top Header */}
        <div className="bg-white border-b border-gray-200 px-6 py-4">
          <h1 className="text-2xl font-bold text-gray-800">
            Stonesbury Mimo Analytics Dashboard
          </h1>
          <p className="text-gray-600 mt-1">
            Geographic Intelligence and Market Analytics Platform
          </p>
        </div>

        {/* Main Content */}
        <div className="flex-1 p-6 bg-gray-50">
          {renderMainContent()}
        </div>
      </div>

      {/* Strategic Report Modal */}
      {showStrategicReport && (
        <StrategicReport
          state={selectedState}
          onClose={() => setShowStrategicReport(false)}
        />
      )}
    </div>
  );
};
