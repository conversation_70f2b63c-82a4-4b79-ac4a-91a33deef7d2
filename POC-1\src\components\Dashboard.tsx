import React, { useState } from 'react';
import { useAuth } from './AuthProvider';
import { DealerMap } from './DealerMap';
import { ImageGallery } from './ImageGallery';
import { MimoForm } from './MimoForm';
import { apiService } from '../services/api';
import { 
  Map, 
  Images, 
  FileText, 
  LogOut, 
  User, 
  Menu, 
  X,
  Home,
  BarChart3
} from 'lucide-react';

type TabType = 'overview' | 'map' | 'gallery' | 'form' | 'analytics';

export const Dashboard: React.FC = () => {
  const { user, logout } = useAuth();
  const [activeTab, setActiveTab] = useState<TabType>('overview');
  const [sidebarOpen, setSidebarOpen] = useState(false);

  const handleFormSubmit = async (formData: any) => {
    try {
      const response = await apiService.submitMimoForm(formData);
      return response;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Submission failed'
      };
    }
  };

  const tabs = [
    { id: 'overview' as TabType, label: 'Overview', icon: Home },
    { id: 'map' as TabType, label: 'Dealer Map', icon: Map },
    { id: 'gallery' as TabType, label: 'Image Gallery', icon: Images },
    { id: 'form' as TabType, label: 'Mimo Form', icon: FileText },
    { id: 'analytics' as TabType, label: 'Analytics', icon: BarChart3 },
  ];

  const renderContent = () => {
    switch (activeTab) {
      case 'overview':
        return (
          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <h2 className="text-2xl font-bold text-gray-800 mb-4">
                Welcome to GeoIQ Analytics Dashboard
              </h2>
              <p className="text-gray-600 mb-6">
                Your comprehensive platform for dealer mapping, image analysis, and form management.
              </p>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {tabs.slice(1).map((tab) => {
                  const Icon = tab.icon;
                  return (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className="p-4 bg-orange-50 hover:bg-orange-100 rounded-lg border border-orange-200 transition-colors text-left"
                    >
                      <Icon className="w-8 h-8 text-orange-500 mb-2" />
                      <h3 className="font-semibold text-gray-800">{tab.label}</h3>
                      <p className="text-sm text-gray-600 mt-1">
                        {tab.id === 'map' && 'View dealer locations on interactive maps'}
                        {tab.id === 'gallery' && 'Browse and analyze uploaded images'}
                        {tab.id === 'form' && 'Submit KPI validation requests'}
                        {tab.id === 'analytics' && 'View performance metrics and reports'}
                      </p>
                    </button>
                  );
                })}
              </div>
            </div>

            {/* Quick Stats */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-white rounded-lg shadow-sm border p-6">
                <div className="flex items-center">
                  <Map className="w-8 h-8 text-blue-500" />
                  <div className="ml-4">
                    <h3 className="text-lg font-semibold text-gray-800">Dealer Locations</h3>
                    <p className="text-gray-600">Interactive mapping system</p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow-sm border p-6">
                <div className="flex items-center">
                  <Images className="w-8 h-8 text-green-500" />
                  <div className="ml-4">
                    <h3 className="text-lg font-semibold text-gray-800">Image Analysis</h3>
                    <p className="text-gray-600">Visual data processing</p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow-sm border p-6">
                <div className="flex items-center">
                  <FileText className="w-8 h-8 text-purple-500" />
                  <div className="ml-4">
                    <h3 className="text-lg font-semibold text-gray-800">Form Management</h3>
                    <p className="text-gray-600">KPI validation system</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );

      case 'map':
        return <DealerMap />;

      case 'gallery':
        return <ImageGallery />;

      case 'form':
        return <MimoForm onSubmit={handleFormSubmit} />;

      case 'analytics':
        return (
          <div className="bg-white rounded-lg shadow-sm border p-6">
            <h2 className="text-2xl font-bold text-gray-800 mb-4">Analytics Dashboard</h2>
            <p className="text-gray-600">
              Analytics features will be integrated here. This section can include:
            </p>
            <ul className="list-disc list-inside mt-4 space-y-2 text-gray-600">
              <li>Performance metrics and KPIs</li>
              <li>Data visualization charts</li>
              <li>Trend analysis reports</li>
              <li>Export functionality</li>
            </ul>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Mobile sidebar overlay */}
      {sidebarOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div className={`fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0 ${
        sidebarOpen ? 'translate-x-0' : '-translate-x-full'
      }`}>
        <div className="flex items-center justify-between h-16 px-6 border-b">
          <h1 className="text-xl font-bold text-gray-800">GeoIQ Dashboard</h1>
          <button
            onClick={() => setSidebarOpen(false)}
            className="lg:hidden text-gray-500 hover:text-gray-700"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        <nav className="mt-6">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => {
                  setActiveTab(tab.id);
                  setSidebarOpen(false);
                }}
                className={`w-full flex items-center px-6 py-3 text-left hover:bg-gray-50 transition-colors ${
                  activeTab === tab.id ? 'bg-orange-50 text-orange-600 border-r-2 border-orange-600' : 'text-gray-700'
                }`}
              >
                <Icon className="w-5 h-5 mr-3" />
                {tab.label}
              </button>
            );
          })}
        </nav>

        <div className="absolute bottom-0 left-0 right-0 p-6 border-t">
          <div className="flex items-center mb-4">
            <User className="w-8 h-8 text-gray-400 mr-3" />
            <div>
              <p className="text-sm font-medium text-gray-800">{user?.email}</p>
              <p className="text-xs text-gray-500">Authenticated User</p>
            </div>
          </div>
          <button
            onClick={logout}
            className="w-full flex items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50 rounded-md transition-colors"
          >
            <LogOut className="w-4 h-4 mr-2" />
            Sign Out
          </button>
        </div>
      </div>

      {/* Main content */}
      <div className="lg:ml-64">
        {/* Top bar */}
        <div className="bg-white shadow-sm border-b h-16 flex items-center justify-between px-6">
          <button
            onClick={() => setSidebarOpen(true)}
            className="lg:hidden text-gray-500 hover:text-gray-700"
          >
            <Menu className="w-6 h-6" />
          </button>
          
          <div className="flex items-center">
            <h2 className="text-lg font-semibold text-gray-800 capitalize">
              {activeTab === 'overview' ? 'Dashboard Overview' : tabs.find(t => t.id === activeTab)?.label}
            </h2>
          </div>

          <div className="flex items-center space-x-4">
            <span className="text-sm text-gray-600">Welcome, {user?.email}</span>
          </div>
        </div>

        {/* Page content */}
        <div className="p-6">
          {renderContent()}
        </div>
      </div>
    </div>
  );
};
