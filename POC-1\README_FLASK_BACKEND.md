# Flask Backend API Documentation

## 🚀 Overview

This Flask backend provides REST API endpoints for the POC-1 project, serving data and functionality for:
- Authentication management
- Dealer mapping and geocoding
- Image gallery management
- Mimo form submissions
- CSV data processing

## 📋 Prerequisites

- Python 3.8+
- All dependencies from `flask_requirements.txt`
- Required CSV data files
- Images directory structure

## 🔧 Installation & Setup

### Quick Start
```bash
# Run the automated setup script
python start_flask.py
```

### Manual Setup
```bash
# Install dependencies
pip install -r flask_requirements.txt

# Create required directories
mkdir submitted_forms

# Start the server
python flask_backend.py
```

## 🌐 API Endpoints

### Authentication Endpoints

#### POST `/api/auth/login`
Login with email and password
```json
{
  "email": "<EMAIL>",
  "password": "user"
}
```

#### POST `/api/auth/logout`
Logout current user

#### GET `/api/auth/check`
Check authentication status

### Map Endpoints

#### GET `/api/map/dealers`
Get all dealer locations with coordinates
```json
{
  "dealers": [
    {
      "name": "Dealer Name",
      "pin": "110001",
      "brand": "Amaron",
      "color": "red",
      "lat": 28.6139,
      "lon": 77.2090,
      "image": "photo.jpg",
      "address": "Full Address"
    }
  ]
}
```

#### POST `/api/map/generate`
Generate interactive map HTML
```json
{
  "center_lat": 28.6139,
  "center_lon": 77.2090,
  "zoom": 11
}
```

### Image Gallery Endpoints

#### GET `/api/images/gallery`
Get images for gallery display
Query parameters:
- `state`: Filter by state (Delhi, West Bengal, etc.)
- `brand`: Filter by brand name
- `show_competitors`: Include competitor images (true/false)

### Form Endpoints

#### POST `/api/form/submit`
Submit Mimo form data
```json
{
  "serviceProvider": "Mimo",
  "kpi": "KPI Name",
  "category": "Products",
  "firstName": "John",
  "lastName": "Doe",
  "email": "<EMAIL>",
  "phone": "**********",
  "address": "Full Address",
  "city": "Delhi",
  "state": "Delhi",
  "pincode": "110001"
}
```

#### GET `/api/form/tickets`
Get list of submitted form tickets

### Data Endpoints

#### GET `/api/data/csv`
Get CSV data for dashboard
Query parameters:
- `file`: CSV filename (optional, defaults to DataValues-Dashboard-CSV.csv)

### Health Check

#### GET `/api/health`
Server health check

## 📁 Required File Structure

```
POC-1/
├── flask_backend.py              # Main Flask application
├── flask_requirements.txt        # Python dependencies
├── start_flask.py               # Startup script
├── amaron_retailer_image.csv    # Amaron dealer data
├── exide_retailer_image.csv     # Exide dealer data
├── tata_green_retailer_image.csv # Tata dealer data
├── luminous_retailer_image.csv  # Luminous dealer data
├── DataValues-Dashboard-CSV.csv # Dashboard data
├── images/                      # Dealer images
├── interactive_images/          # Gallery images
│   ├── Delhi/
│   └── West Bengal/
└── submitted_forms/             # Form submissions (auto-created)
```

## 🔐 Authentication

The backend uses session-based authentication:
- Login creates a server-side session
- Session cookies are used for subsequent requests
- CORS is configured to support credentials

### Default Credentials
- Email: `<EMAIL>`, Password: `user`
- Email: `<EMAIL>`, Password: `admin`

## 🗺️ Geocoding

The backend uses Nominatim (OpenStreetMap) for geocoding:
- Converts PIN codes to latitude/longitude coordinates
- Includes rate limiting to respect API limits
- Caches results to improve performance

## 📸 Image Processing

Images are processed and served as base64 data:
- Supports JPG, JPEG, PNG, GIF, BMP formats
- Images are encoded to base64 for API responses
- Organized by state and brand folders

## 🔧 Configuration

### Environment Variables
```bash
FLASK_ENV=development
FLASK_DEBUG=1
OPENAI_API_KEY=your-key-here  # Optional, for AI features
```

### CORS Configuration
The backend is configured to accept requests from:
- `http://localhost:3000` (React development server)
- `http://localhost:5173` (Vite development server)

## 🚨 Error Handling

All endpoints return consistent error responses:
```json
{
  "error": "Error message description",
  "status": "error"
}
```

## 📊 Performance Considerations

- Geocoding requests are rate-limited
- Images are cached after first load
- Session data is stored server-side
- Large CSV files are processed efficiently with pandas

## 🔄 Integration with React Frontend

The React frontend (`sales-ys` project) integrates with this backend:

1. **API Service**: `src/services/api.ts` handles all API calls
2. **Authentication**: `AuthProvider` manages login state
3. **Components**: Each component calls appropriate endpoints
4. **CORS**: Configured for cross-origin requests

### React Component Mapping
- `LoginForm` → `/api/auth/*`
- `DealerMap` → `/api/map/*`
- `ImageGallery` → `/api/images/*`
- `MimoForm` → `/api/form/*`

## 🧪 Testing

Test the API endpoints:
```bash
# Health check
curl http://localhost:5000/api/health

# Login
curl -X POST http://localhost:5000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"user"}'

# Get dealers
curl http://localhost:5000/api/map/dealers
```

## 🚀 Production Deployment

For production deployment:

1. Use a production WSGI server (Gunicorn)
2. Set proper environment variables
3. Configure reverse proxy (Nginx)
4. Use a proper database instead of file storage
5. Implement proper logging and monitoring

```bash
# Install Gunicorn
pip install gunicorn

# Run with Gunicorn
gunicorn -w 4 -b 0.0.0.0:5000 flask_backend:app
```

## 📝 Troubleshooting

### Common Issues

1. **Import Errors**: Install missing dependencies
   ```bash
   pip install -r flask_requirements.txt
   ```

2. **CORS Errors**: Check frontend URL in CORS configuration

3. **File Not Found**: Ensure all CSV and image files exist

4. **Geocoding Fails**: Check internet connection and rate limits

5. **Session Issues**: Clear browser cookies and restart server

### Debug Mode
Run with debug logging:
```bash
FLASK_DEBUG=1 python flask_backend.py
```
