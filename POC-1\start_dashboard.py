#!/usr/bin/env python3
"""
Unified Startup Script for Stonesbury Mimo Analytics Dashboard
Starts both Flask backend and React frontend with proper coordination
"""

import subprocess
import sys
import os
import time
import signal
import threading
from pathlib import Path
import webbrowser

class AppLauncher:
    def __init__(self):
        self.flask_process = None
        self.react_process = None
        self.running = True
        
    def check_dependencies(self):
        """Check if required dependencies are installed"""
        print("🔍 Checking dependencies...")
        
        # Check Python dependencies
        try:
            import flask
            import pandas
            import folium
            print("✅ Python dependencies found")
        except ImportError as e:
            print(f"❌ Missing Python dependency: {e}")
            print("💡 Run: pip install -r requirements.txt")
            return False
            
        # Check Node.js and npm
        try:
            result = subprocess.run(['node', '--version'], capture_output=True, text=True, shell=True)
            if result.returncode == 0:
                print(f"✅ Node.js found: {result.stdout.strip()}")
            else:
                print("❌ Node.js not found")
                return False

            result = subprocess.run(['npm', '--version'], capture_output=True, text=True, shell=True)
            if result.returncode == 0:
                print(f"✅ npm found: {result.stdout.strip()}")
            else:
                print("❌ npm not found")
                return False

        except Exception as e:
            print(f"❌ Error checking Node.js/npm: {e}")
            print("💡 Install Node.js from https://nodejs.org/")
            return False
            
        return True
        
    def install_node_dependencies(self):
        """Install Node.js dependencies if needed"""
        if not os.path.exists('node_modules'):
            print("📦 Installing Node.js dependencies...")
            try:
                result = subprocess.run(['npm', 'install'], check=True, capture_output=True, text=True, shell=True)
                print("✅ Node.js dependencies installed")
                return True
            except subprocess.CalledProcessError as e:
                print(f"❌ Failed to install Node.js dependencies: {e}")
                print(f"Error output: {e.stderr}")
                return False
        else:
            print("✅ Node.js dependencies already installed")
            return True
            
    def start_flask_backend(self):
        """Start the Flask backend server"""
        print("🚀 Starting Flask backend server...")
        try:
            self.flask_process = subprocess.Popen(
                [sys.executable, 'flask_backend.py'],
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                bufsize=1,
                universal_newlines=True
            )
            
            # Wait a moment for Flask to start
            time.sleep(3)
            
            if self.flask_process.poll() is None:
                print("✅ Flask backend started successfully")
                return True
            else:
                print("❌ Flask backend failed to start")
                return False
                
        except Exception as e:
            print(f"❌ Failed to start Flask backend: {e}")
            return False
            
    def start_react_frontend(self):
        """Start the React frontend development server"""
        print("🚀 Starting React frontend server...")
        try:
            self.react_process = subprocess.Popen(
                ['npm', 'run', 'dev'],
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                bufsize=1,
                universal_newlines=True,
                shell=True
            )
            
            # Wait a moment for React to start
            time.sleep(5)
            
            if self.react_process.poll() is None:
                print("✅ React frontend started successfully")
                return True
            else:
                print("❌ React frontend failed to start")
                return False
                
        except Exception as e:
            print(f"❌ Failed to start React frontend: {e}")
            return False
            
    def open_browser(self):
        """Open the application in the default browser"""
        try:
            time.sleep(2)  # Wait a bit more for servers to be ready
            print("🌐 Opening application in browser...")
            webbrowser.open('http://localhost:3002')
        except Exception as e:
            print(f"⚠️ Could not open browser automatically: {e}")
            print("📱 Please manually open: http://localhost:3002")
            
    def cleanup(self):
        """Clean up processes on exit"""
        print("\n🛑 Shutting down servers...")
        self.running = False
        
        if self.flask_process:
            try:
                self.flask_process.terminate()
                self.flask_process.wait(timeout=5)
                print("✅ Flask backend stopped")
            except subprocess.TimeoutExpired:
                self.flask_process.kill()
                print("🔥 Flask backend force killed")
            except Exception as e:
                print(f"⚠️ Error stopping Flask: {e}")
                
        if self.react_process:
            try:
                self.react_process.terminate()
                self.react_process.wait(timeout=5)
                print("✅ React frontend stopped")
            except subprocess.TimeoutExpired:
                self.react_process.kill()
                print("🔥 React frontend force killed")
            except Exception as e:
                print(f"⚠️ Error stopping React: {e}")
                
    def run(self):
        """Main run method"""
        print("🎯 Stonesbury Mimo Analytics Dashboard Launcher")
        print("=" * 50)
        
        # Check dependencies
        if not self.check_dependencies():
            return False
            
        # Install Node dependencies
        if not self.install_node_dependencies():
            return False
            
        # Start Flask backend
        if not self.start_flask_backend():
            return False
            
        # Start React frontend
        if not self.start_react_frontend():
            self.cleanup()
            return False
            
        print("\n🎉 Both servers started successfully!")
        print("📱 Frontend: http://localhost:3002")
        print("🔧 Backend API: http://localhost:5000")
        print("🔑 Login credentials:")
        print("   - <EMAIL> / user1234")
        print("   - <EMAIL> / user1234")
        print("   - <EMAIL> / user1234")
        
        # Open browser
        browser_thread = threading.Thread(target=self.open_browser, daemon=True)
        browser_thread.start()
        
        print("\n⏹️  Press Ctrl+C to stop both servers")
        
        try:
            # Keep the main thread alive
            while self.running:
                time.sleep(1)
                
                # Check if processes are still running
                if self.flask_process and self.flask_process.poll() is not None:
                    print("❌ Flask backend stopped unexpectedly")
                    break
                    
                if self.react_process and self.react_process.poll() is not None:
                    print("❌ React frontend stopped unexpectedly")
                    break
                    
        except KeyboardInterrupt:
            print("\n👋 Received shutdown signal")
            
        finally:
            self.cleanup()
            
        return True

def signal_handler(signum, frame):
    """Handle system signals"""
    print(f"\n🛑 Received signal {signum}")
    sys.exit(0)

if __name__ == "__main__":
    # Set up signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Change to script directory
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    # Launch the application
    launcher = AppLauncher()
    success = launcher.run()
    
    if success:
        print("✅ Application shutdown completed successfully")
        sys.exit(0)
    else:
        print("❌ Application failed to start")
        sys.exit(1)
