import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { apiService } from '../services/api';

interface User {
  email: string;
}

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  checkAuth: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const isAuthenticated = !!user;

  const checkAuth = async () => {
    try {
      setIsLoading(true);
      console.log('Checking authentication...');

      // Test if backend is reachable first
      try {
        const healthResponse = await fetch('/api/health', {
          method: 'GET',
          credentials: 'include',
          timeout: 2000
        } as any);
        console.log('Health check status:', healthResponse.status);
      } catch (healthError) {
        console.error('Backend not reachable:', healthError);
        throw new Error('Backend not available');
      }

      // Add timeout to prevent hanging
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Authentication check timeout')), 3000)
      );

      const authPromise = apiService.checkAuth();
      const response = await Promise.race([authPromise, timeoutPromise]);

      console.log('Auth check response:', response);

      if (response && typeof response === 'object' && 'authenticated' in response && 'user' in response) {
        const authResponse = response as { authenticated: boolean; user: User };
        if (authResponse.authenticated && authResponse.user) {
          setUser(authResponse.user);
          console.log('User authenticated:', authResponse.user);
        } else {
          setUser(null);
          console.log('User not authenticated');
        }
      } else {
        setUser(null);
        console.log('User not authenticated - invalid response');
      }
    } catch (error) {
      console.error('Auth check failed:', error);
      // Don't stay in loading state forever - show login form
      setUser(null);
    } finally {
      setIsLoading(false);
    }
  };

  const login = async (email: string, password: string) => {
    try {
      setIsLoading(true);
      const response = await apiService.login(email, password);
      
      if (response.success && response.user) {
        setUser(response.user);
      } else {
        throw new Error(response.message || 'Login failed');
      }
    } catch (error) {
      console.error('Login failed:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    try {
      setIsLoading(true);
      await apiService.logout();
      setUser(null);
    } catch (error) {
      console.error('Logout failed:', error);
      // Still clear user state even if logout request fails
      setUser(null);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    // Add a maximum loading time - if auth check takes too long, show login form
    const maxLoadingTimer = setTimeout(() => {
      if (isLoading) {
        console.log('Auth check taking too long, showing login form');
        setIsLoading(false);
        setUser(null);
      }
    }, 3000); // 3 second timeout

    checkAuth().finally(() => {
      clearTimeout(maxLoadingTimer);
    });

    return () => {
      clearTimeout(maxLoadingTimer);
    };
  }, []);

  const value: AuthContextType = {
    user,
    isAuthenticated,
    isLoading,
    login,
    logout,
    checkAuth,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
