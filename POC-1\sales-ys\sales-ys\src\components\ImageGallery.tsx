import React, { useState, useEffect } from 'react';
import { Search, Filter, Eye, Download, Calendar, MapPin, X, ChevronLeft, ChevronRight } from 'lucide-react';

interface GalleryImage {
  name: string;
  folder: string;
  brand: string;
  base64: string;
  path: string;
  state: string;
  timestamp: string;
}

interface ImageGalleryProps {
  state?: string;
  onStateChange?: (state: string) => void;
}

export const ImageGallery: React.FC<ImageGalleryProps> = ({ 
  state = 'Delhi', 
  onStateChange 
}) => {
  const [images, setImages] = useState<GalleryImage[]>([]);
  const [filteredImages, setFilteredImages] = useState<GalleryImage[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedBrand, setSelectedBrand] = useState('');
  const [showCompetitors, setShowCompetitors] = useState(false);
  const [selectedImage, setSelectedImage] = useState<GalleryImage | null>(null);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  const states = ['Delhi', 'West Bengal', 'Maharashtra', 'Karnataka', 'Tamil Nadu'];

  useEffect(() => {
    loadImages();
  }, [state, showCompetitors]);

  useEffect(() => {
    filterImages();
  }, [images, searchQuery, selectedBrand]);

  const loadImages = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const params = new URLSearchParams({
        state,
        show_competitors: showCompetitors.toString()
      });
      
      if (selectedBrand) {
        params.append('brand', selectedBrand);
      }
      
      const response = await fetch(`/api/images/gallery?${params}`);
      if (!response.ok) {
        throw new Error('Failed to load images');
      }
      
      const data = await response.json();
      setImages(data.images || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load images');
    } finally {
      setLoading(false);
    }
  };

  const filterImages = () => {
    let filtered = images;

    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(img =>
        img.name.toLowerCase().includes(query) ||
        img.brand.toLowerCase().includes(query) ||
        img.folder.toLowerCase().includes(query)
      );
    }

    if (selectedBrand) {
      filtered = filtered.filter(img =>
        img.brand.toLowerCase().includes(selectedBrand.toLowerCase())
      );
    }

    setFilteredImages(filtered);
  };

  const openModal = (image: GalleryImage, index: number) => {
    setSelectedImage(image);
    setCurrentImageIndex(index);
  };

  const closeModal = () => {
    setSelectedImage(null);
  };

  const navigateImage = (direction: 'prev' | 'next') => {
    const newIndex = direction === 'prev' 
      ? (currentImageIndex - 1 + filteredImages.length) % filteredImages.length
      : (currentImageIndex + 1) % filteredImages.length;
    
    setCurrentImageIndex(newIndex);
    setSelectedImage(filteredImages[newIndex]);
  };

  const downloadImage = (image: GalleryImage) => {
    const link = document.createElement('a');
    link.href = `data:image/jpeg;base64,${image.base64}`;
    link.download = image.name;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const uniqueBrands = Array.from(new Set(images.map(img => img.brand)));

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96 bg-gray-50 rounded-lg">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading images...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
        <p className="text-red-600 font-medium">Error loading images</p>
        <p className="text-red-500 text-sm mt-1">{error}</p>
        <button
          onClick={loadImages}
          className="mt-3 px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Controls */}
      <div className="bg-white p-4 rounded-lg shadow-sm border">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {/* State Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">State</label>
            <select
              value={state}
              onChange={(e) => onStateChange?.(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 outline-none"
            >
              {states.map(s => (
                <option key={s} value={s}>{s}</option>
              ))}
            </select>
          </div>

          {/* Brand Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Brand</label>
            <select
              value={selectedBrand}
              onChange={(e) => setSelectedBrand(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 outline-none"
            >
              <option value="">All Brands</option>
              {uniqueBrands.map(brand => (
                <option key={brand} value={brand}>{brand}</option>
              ))}
            </select>
          </div>

          {/* Search */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Search</label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search images..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 outline-none"
              />
            </div>
          </div>

          {/* Options */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Options</label>
            <div className="flex items-center">
              <input
                type="checkbox"
                id="showCompetitors"
                checked={showCompetitors}
                onChange={(e) => setShowCompetitors(e.target.checked)}
                className="mr-2"
              />
              <label htmlFor="showCompetitors" className="text-sm text-gray-700">
                Show Competitors
              </label>
            </div>
          </div>
        </div>

        {/* Stats */}
        <div className="mt-4 flex items-center gap-4 text-sm text-gray-600">
          <span>Total Images: {images.length}</span>
          <span>Showing: {filteredImages.length}</span>
          <span>State: {state}</span>
        </div>
      </div>

      {/* Gallery Grid */}
      {filteredImages.length > 0 ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {filteredImages.map((image, index) => (
            <div
              key={`${image.path}-${index}`}
              className="bg-white rounded-lg shadow-sm border overflow-hidden hover:shadow-md transition-shadow cursor-pointer"
              onClick={() => openModal(image, index)}
            >
              <div className="aspect-square relative overflow-hidden">
                <img
                  src={`data:image/jpeg;base64,${image.base64}`}
                  alt={image.name}
                  className="w-full h-full object-cover hover:scale-105 transition-transform duration-200"
                  loading="lazy"
                />
                <div className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-20 transition-all duration-200 flex items-center justify-center">
                  <Eye className="w-8 h-8 text-white opacity-0 hover:opacity-100 transition-opacity" />
                </div>
              </div>
              
              <div className="p-3">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-orange-600 bg-orange-50 px-2 py-1 rounded">
                    {image.brand}
                  </span>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      downloadImage(image);
                    }}
                    className="text-gray-400 hover:text-gray-600 transition-colors"
                  >
                    <Download className="w-4 h-4" />
                  </button>
                </div>
                
                <p className="text-sm text-gray-800 font-medium truncate mb-1">
                  {image.name}
                </p>
                
                <div className="flex items-center text-xs text-gray-500 space-x-2">
                  <Calendar className="w-3 h-3" />
                  <span>{image.timestamp}</span>
                </div>
                
                <div className="flex items-center text-xs text-gray-500 space-x-2 mt-1">
                  <MapPin className="w-3 h-3" />
                  <span>{image.state}</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-12 bg-gray-50 rounded-lg">
          <Filter className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No images found</h3>
          <p className="text-gray-500">
            {searchQuery || selectedBrand
              ? 'Try adjusting your search criteria'
              : 'No images available for the selected state'}
          </p>
        </div>
      )}

      {/* Modal */}
      {selectedImage && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-4xl max-h-full overflow-hidden">
            <div className="flex items-center justify-between p-4 border-b">
              <div>
                <h3 className="text-lg font-semibold">{selectedImage.name}</h3>
                <p className="text-sm text-gray-600">{selectedImage.brand} - {selectedImage.state}</p>
              </div>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => downloadImage(selectedImage)}
                  className="p-2 text-gray-600 hover:text-gray-800 transition-colors"
                >
                  <Download className="w-5 h-5" />
                </button>
                <button
                  onClick={closeModal}
                  className="p-2 text-gray-600 hover:text-gray-800 transition-colors"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>
            </div>
            
            <div className="relative">
              <img
                src={`data:image/jpeg;base64,${selectedImage.base64}`}
                alt={selectedImage.name}
                className="max-w-full max-h-96 object-contain mx-auto"
              />
              
              {filteredImages.length > 1 && (
                <>
                  <button
                    onClick={() => navigateImage('prev')}
                    className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-75 transition-all"
                  >
                    <ChevronLeft className="w-6 h-6" />
                  </button>
                  <button
                    onClick={() => navigateImage('next')}
                    className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-75 transition-all"
                  >
                    <ChevronRight className="w-6 h-6" />
                  </button>
                </>
              )}
            </div>
            
            <div className="p-4 border-t bg-gray-50">
              <div className="flex items-center justify-between text-sm text-gray-600">
                <span>Image {currentImageIndex + 1} of {filteredImages.length}</span>
                <div className="flex items-center space-x-4">
                  <span className="flex items-center">
                    <Calendar className="w-4 h-4 mr-1" />
                    {selectedImage.timestamp}
                  </span>
                  <span className="flex items-center">
                    <MapPin className="w-4 h-4 mr-1" />
                    {selectedImage.state}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
