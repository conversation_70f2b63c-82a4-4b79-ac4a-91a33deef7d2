#!/usr/bin/env python3
"""Test script to verify all file paths are working correctly"""

import os
import pandas as pd

def get_smart_base_directory():
    """Smart base directory detection that works on all systems"""
    current_dir = os.getcwd()

    # Smart base directory detection
    base_dir = current_dir

    # Method 1: Check if we're already in the right directory
    if os.path.exists(os.path.join(current_dir, 'Centres')):
        base_dir = current_dir
    # Method 2: Check if we're in pages directory
    elif current_dir.endswith('pages') and os.path.exists(os.path.join(os.path.dirname(current_dir), 'Centres')):
        base_dir = os.path.dirname(current_dir)
    # Method 3: Look for POC-1 subdirectory in current directory
    elif os.path.exists(os.path.join(current_dir, 'POC-1', 'Centres')):
        base_dir = os.path.join(current_dir, 'POC-1')
    # Method 4: Look for POC-1 directory in current path
    elif 'POC-1' in current_dir:
        # Find the POC-1 directory in the path
        path_parts = current_dir.split(os.sep)
        poc_index = -1
        for i, part in enumerate(path_parts):
            if 'POC-1' in part:
                poc_index = i
                break
        if poc_index >= 0:
            base_dir = os.sep.join(path_parts[:poc_index + 1])
    # Method 5: Search upward but limit to 3 levels to avoid going to root
    else:
        search_dir = current_dir
        for _ in range(3):  # Limit search to 3 levels up
            if os.path.exists(os.path.join(search_dir, 'Centres')):
                base_dir = search_dir
                break
            parent = os.path.dirname(search_dir)
            if parent == search_dir:  # Reached root
                break
            search_dir = parent

    return base_dir

def test_file_paths():
    """Test all critical file paths used in the Interactive Map"""
    print("🔍 Testing file paths...")

    # Get base directory
    current_dir = os.getcwd()
    print(f"Current directory: {current_dir}")

    base_dir = get_smart_base_directory()
    print(f"Base directory: {base_dir}")

    # Verify base directory is correct
    if not os.path.exists(os.path.join(base_dir, 'Centres')):
        print(f"❌ Base directory detection failed! No 'Centres' folder found in {base_dir}")
        return False
    
    # Test Delhi Excel file paths
    print("\n📊 Testing Delhi Excel file paths:")
    delhi_paths = [
        os.path.join(base_dir, "Centres", "Delhi", "YusufSaraiAmaronCentre-FinalCSV.xlsx"),
        os.path.join(base_dir, "Centres", "Delhi", "YusufSaraiAmaronCentre-FinalCSV.xls"),
        os.path.join(base_dir, "YusufSaraiAmaronCentre-FinalCSV.xlsx"),
        "Centres/Delhi/YusufSaraiAmaronCentre-FinalCSV.xlsx",
        "YusufSaraiAmaronCentre-FinalCSV.xlsx"
    ]
    
    delhi_found = False
    for path in delhi_paths:
        if os.path.exists(path):
            print(f"✅ Found Delhi file: {path}")
            delhi_found = True
            
            # Test reading the file
            try:
                df = pd.read_excel(path)
                print(f"   📋 Loaded {len(df)} rows with columns: {df.columns.tolist()}")
            except Exception as e:
                print(f"   ❌ Error reading file: {e}")
            break
        else:
            print(f"❌ Not found: {path}")
    
    if not delhi_found:
        print("❌ No Delhi Excel file found!")
    
    # Test West Bengal CSV file paths
    print("\n📊 Testing West Bengal CSV file paths:")
    wb_paths = [
        os.path.join(base_dir, "Centres", "West bengal", "South24ParganasAmaronCenter-CSV.csv"),
        os.path.join(base_dir, "Centres", "West Bengal", "South24ParganasAmaronCenter-CSV.csv"),
        "Centres/West bengal/South24ParganasAmaronCenter-CSV.csv",
        "Centres/West Bengal/South24ParganasAmaronCenter-CSV.csv"
    ]
    
    wb_found = False
    for path in wb_paths:
        if os.path.exists(path):
            print(f"✅ Found West Bengal file: {path}")
            wb_found = True
            
            # Test reading the file
            try:
                # Try different encodings
                encodings = ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1']
                for encoding in encodings:
                    try:
                        df = pd.read_csv(path, encoding=encoding)
                        print(f"   📋 Loaded {len(df)} rows with {encoding} encoding")
                        print(f"   📋 Columns: {df.columns.tolist()}")
                        break
                    except UnicodeDecodeError:
                        continue
            except Exception as e:
                print(f"   ❌ Error reading file: {e}")
            break
        else:
            print(f"❌ Not found: {path}")
    
    if not wb_found:
        print("❌ No West Bengal CSV file found!")
    
    # Test logo paths
    print("\n🖼️ Testing logo file paths:")
    logo_paths = [
        os.path.join(base_dir, "logo", "Stonesbury-logo.png"),
        os.path.join(base_dir, "logo", "Adobe Express - file.png"),
        "logo/Stonesbury-logo.png",
        "logo/Adobe Express - file.png"
    ]
    
    for path in logo_paths:
        if os.path.exists(path):
            print(f"✅ Found logo: {path}")
        else:
            print(f"❌ Not found: {path}")
    
    # Test interactive images paths
    print("\n📸 Testing interactive images paths:")
    image_paths = [
        os.path.join(base_dir, "interactive_images"),
        "interactive_images",
        "../interactive_images"
    ]
    
    images_found = False
    for path in image_paths:
        if os.path.exists(path):
            print(f"✅ Found images directory: {path}")
            images_found = True
            
            # List subdirectories
            try:
                subdirs = [d for d in os.listdir(path) if os.path.isdir(os.path.join(path, d))]
                print(f"   📁 Subdirectories: {subdirs}")
            except Exception as e:
                print(f"   ❌ Error listing subdirectories: {e}")
            break
        else:
            print(f"❌ Not found: {path}")
    
    if not images_found:
        print("❌ No interactive images directory found!")
    
    # Summary
    print("\n📋 Summary:")
    print(f"✅ Delhi data: {'Found' if delhi_found else 'Missing'}")
    print(f"✅ West Bengal data: {'Found' if wb_found else 'Missing'}")
    print(f"✅ Interactive images: {'Found' if images_found else 'Missing'}")
    
    if delhi_found and wb_found:
        print("\n🎉 All critical data files found! The map should work now.")
        return True
    else:
        print("\n💥 Some critical files are missing. Check the paths above.")
        return False

if __name__ == "__main__":
    test_file_paths()
