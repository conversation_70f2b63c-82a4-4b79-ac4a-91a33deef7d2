#!/usr/bin/env python3
"""
Test script for image gallery API functionality
"""

import os
import sys
sys.path.append('.')

def test_image_gallery_api():
    """Test the image gallery API logic with state mapping"""
    print("Testing image gallery API functionality...")
    
    # Test state mapping
    state_folder_map = {
        'Delhi(NCT)': 'Delhi',
        'Delhi': 'Delhi',
        'West Bengal': 'West Bengal'
    }
    
    # Test with Delhi(NCT) state (as sent from frontend)
    state = 'Delhi(NCT)'
    brand_filter = 'Amaron'
    
    folder_name = state_folder_map.get(state, state)
    base_path = f"interactive_images/{folder_name}"
    
    print(f"State: '{state}' -> Folder: '{folder_name}'")
    print(f"Base path: {base_path}")
    print(f"Path exists: {os.path.exists(base_path)}")
    
    if not os.path.exists(base_path):
        print("ERROR: Base path does not exist!")
        return False
    
    # Test image processing
    images_found = []
    
    for root, dirs, files in os.walk(base_path):
        folder_name = os.path.basename(root)
        print(f"\nProcessing folder: {folder_name}")
        
        for file in files:
            if file.lower().endswith(('.jpg', '.jpeg', '.png', '.gif', '.bmp')):
                file_path = os.path.join(root, file)
                
                # Brand filtering
                if brand_filter and brand_filter != "All Brands":
                    brand_in_folder = brand_filter.lower() in folder_name.lower()
                    print(f"  Brand filter '{brand_filter}' in folder '{folder_name}': {brand_in_folder}")
                    if not brand_in_folder:
                        continue
                
                # Extract brand name
                brand_name = folder_name
                if '-' in folder_name:
                    brand_name = folder_name.split('-')[-1]
                
                print(f"  Found image: {file} (brand: {brand_name})")
                images_found.append({
                    'name': file,
                    'folder': folder_name,
                    'brand': brand_name,
                    'path': file_path
                })
    
    print(f"\nTotal images found: {len(images_found)}")
    
    # Test with show_competitors = True
    print(f"\n--- Testing with show_competitors = True ---")
    all_images = []
    
    for root, dirs, files in os.walk(base_path):
        folder_name = os.path.basename(root)
        print(f"Processing folder: {folder_name}")
        
        image_count = len([f for f in files if f.lower().endswith(('.jpg', '.jpeg', '.png', '.gif', '.bmp'))])
        print(f"  Images in folder: {image_count}")
        all_images.extend([f for f in files if f.lower().endswith(('.jpg', '.jpeg', '.png', '.gif', '.bmp'))])
    
    print(f"Total images (all brands): {len(all_images)}")
    
    return len(images_found) > 0

if __name__ == "__main__":
    success = test_image_gallery_api()
    if success:
        print("\n✅ Image gallery API test PASSED")
    else:
        print("\n❌ Image gallery API test FAILED")
