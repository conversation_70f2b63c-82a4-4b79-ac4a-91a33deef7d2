<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Detailed Analytics Form - GeoIQ</title>
  <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="min-h-screen flex">

  <!-- Left Side: Form -->
  <div class="w-full md:w-1/2 bg-white flex flex-col justify-center relative px-10 lg:px-20 py-10">

    <!-- Logo at Top Left -->
    <div class="absolute top-6 left-6 flex gap-3">
      <img src="logo/Stonesbury-logo.png" alt="Stonesbury Logo" class="h-16 w-auto" />
      <img src="logo/Adobe Express - file.png" alt="Adobe Express Logo" class="h-16 w-auto" />
    </div>

    <!-- Form Content -->
    <div class="max-w-md mx-auto w-full">
      <h1 class="text-3xl font-bold text-gray-900 mb-2">Detailed Analytics</h1>
      <p class="text-gray-600 mb-8">Enter the required information for detailed analysis</p>

      <!-- KPI Information Display -->
      <div id="kpiInfo" class="mb-6 p-4 bg-orange-50 border border-orange-200 rounded-lg">
        <h3 class="text-lg font-semibold text-orange-800 mb-2">📊 Analysis Context</h3>
        <div class="text-sm text-orange-700">
          <p><strong>KPI:</strong> <span id="kpiNumber">-</span></p>
          <p><strong>Category:</strong> <span id="category">-</span></p>
          <p><strong>Sub-Category:</strong> <span id="subCategory">-</span></p>
        </div>
      </div>

      <form id="analyticsForm" class="space-y-4">
        <div>
          <label class="text-sm font-medium text-gray-700">Business Location</label>
          <input type="text" name="location" required class="mt-1 w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 outline-none" placeholder="Enter business location" />
        </div>
        
        <div>
          <label class="text-sm font-medium text-gray-700">Market Segment</label>
          <select name="segment" required class="mt-1 w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 outline-none">
            <option value="">Select market segment</option>
            <option value="retail">Retail</option>
            <option value="wholesale">Wholesale</option>
            <option value="commercial">Commercial</option>
            <option value="residential">Residential</option>
          </select>
        </div>
        
        <div>
          <label class="text-sm font-medium text-gray-700">Target Demographics</label>
          <input type="text" name="demographics" required class="mt-1 w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 outline-none" placeholder="e.g., Age 25-45, Income 5L+" />
        </div>
        
        <div>
          <label class="text-sm font-medium text-gray-700">Competition Analysis</label>
          <textarea name="competition" required class="mt-1 w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 outline-none" rows="3" placeholder="Describe competitive landscape"></textarea>
        </div>
        
        <div>
          <label class="text-sm font-medium text-gray-700">Revenue Projection (₹)</label>
          <input type="number" name="revenue" required class="mt-1 w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 outline-none" placeholder="Enter projected revenue" />
        </div>
        
        <div>
          <label class="text-sm font-medium text-gray-700">Investment Required (₹)</label>
          <input type="number" name="investment" required class="mt-1 w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 outline-none" placeholder="Enter required investment" />
        </div>
        
        <div>
          <label class="text-sm font-medium text-gray-700">Additional Notes</label>
          <textarea name="notes" class="mt-1 w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 outline-none" rows="3" placeholder="Any additional information or requirements"></textarea>
        </div>

        <!-- Loading State -->
        <div id="loadingState" class="hidden text-center py-4">
          <div class="inline-flex items-center">
            <svg class="animate-spin h-5 w-5 text-orange-500 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <span class="text-orange-600">Processing analysis...</span>
          </div>
        </div>

        <button type="submit" id="submitBtn" class="w-full bg-orange-500 text-white py-2 rounded-md hover:bg-orange-600 transition-all flex items-center justify-center">
          <span id="submitText">Submit Analysis</span>
          <div id="loadingSpinner" class="hidden ml-2">
            <svg class="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          </div>
        </button>
      </form>

      <div class="mt-6 text-center">
        <button onclick="window.close()" class="text-orange-600 hover:text-orange-800 text-sm font-medium">
          ← Close Window
        </button>
      </div>
    </div>
  </div>

  <!-- Right Side: Gradient Background -->
  <div class="hidden md:block md:w-1/2 bg-gradient-to-br from-orange-400 via-orange-500 to-orange-600 relative overflow-hidden">
    <div class="absolute inset-0 bg-black bg-opacity-20"></div>
    
    <!-- Decorative Elements -->
    <div class="absolute top-20 left-10 w-32 h-32 bg-white bg-opacity-10 rounded-full blur-xl"></div>
    <div class="absolute bottom-20 right-10 w-40 h-40 bg-white bg-opacity-10 rounded-full blur-xl"></div>
    <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-white bg-opacity-5 rounded-full blur-2xl"></div>
    
    <!-- Content -->
    <div class="relative z-10 h-full flex flex-col justify-center items-center text-white px-12">
      <div class="text-center">
        <h2 class="text-4xl font-bold mb-6">Advanced Analytics</h2>
        <p class="text-xl mb-8 text-orange-100">
          Comprehensive market analysis and business intelligence for informed decision making.
        </p>
        <div class="space-y-4 text-left">
          <div class="flex items-center">
            <div class="w-2 h-2 bg-white rounded-full mr-3"></div>
            <span>Market Segmentation Analysis</span>
          </div>
          <div class="flex items-center">
            <div class="w-2 h-2 bg-white rounded-full mr-3"></div>
            <span>Competitive Intelligence</span>
          </div>
          <div class="flex items-center">
            <div class="w-2 h-2 bg-white rounded-full mr-3"></div>
            <span>Revenue Forecasting</span>
          </div>
          <div class="flex items-center">
            <div class="w-2 h-2 bg-white rounded-full mr-3"></div>
            <span>Investment Planning</span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
    // Extract URL parameters
    function getUrlParams() {
      const params = new URLSearchParams(window.location.search);
      return {
        kpi: params.get('kpi') || 'N/A',
        category: params.get('category') || 'N/A',
        subcategory: params.get('subcategory') || 'N/A',
        row: params.get('row') || 'N/A'
      };
    }

    // Populate KPI information
    function populateKpiInfo() {
      const params = getUrlParams();
      document.getElementById('kpiNumber').textContent = params.kpi;
      document.getElementById('category').textContent = params.category;
      document.getElementById('subCategory').textContent = params.subcategory;
    }

    // Show loading state
    function showLoading() {
      document.getElementById('submitText').textContent = 'Processing...';
      document.getElementById('loadingSpinner').classList.remove('hidden');
      document.getElementById('submitBtn').disabled = true;
      document.getElementById('loadingState').classList.remove('hidden');
    }

    // Hide loading state
    function hideLoading() {
      document.getElementById('submitText').textContent = 'Submit Analysis';
      document.getElementById('loadingSpinner').classList.add('hidden');
      document.getElementById('submitBtn').disabled = false;
      document.getElementById('loadingState').classList.add('hidden');
    }

    // Handle form submission
    document.getElementById('analyticsForm').addEventListener('submit', function(e) {
      e.preventDefault();
      
      // Get form data
      const formData = new FormData(e.target);
      const data = Object.fromEntries(formData.entries());
      
      // Add KPI context
      const params = getUrlParams();
      data.kpi = params.kpi;
      data.category = params.category;
      data.subcategory = params.subcategory;
      data.row = params.row;

      showLoading();

      // Simulate processing time
      setTimeout(() => {
        hideLoading();
        
        // Show success message
        alert('Analysis submitted successfully! The data has been processed and will be reviewed by our analytics team.');
        
        // Optionally close the window or redirect
        // window.close();
      }, 2000);
    });

    // Initialize page
    window.addEventListener('load', function() {
      populateKpiInfo();
    });
  </script>

</body>
</html>
